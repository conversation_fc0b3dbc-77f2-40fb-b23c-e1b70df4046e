/*******************************************************
  * Name    :BMS_only.h
  * Author  :<PERSON><PERSON><PERSON><PERSON> @WangHu
  * Function:****
  * Version :V1.0.0
  * File    :BMS_only
  * Data    :2025.07.04 09:26:13
*******************************************************/
//BMS
typedef union
{
    uint8_t data[8];
    struct
    {
        //Byte[0]
        bits_t reserved0_7                        :8;
        //Byte[1]
        bits_t reserved8_15                       :8;
        //Byte[2]
        bits_t reserved16_23                      :8;
        //Byte[3]
        bits_t reserved24_31                      :8;
        //Byte[4]
        bits_t reserved32_39                      :8;
        //Byte[5]
        bits_t reserved40_47                      :8;
        //Byte[6]
        bits_t reserved48_55                      :8;
        //Byte[7]
        bits_t reserved56_63                      :8;
    }bits;
}CanMatrix_BMSMsg_Type;



