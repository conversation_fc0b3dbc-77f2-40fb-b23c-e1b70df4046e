VERSION "BMS CAN FD V2.0"


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: Project test

BA_DEF_ "VFrameFormat" ENUM "StandardCAN","ExtendedCAN","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_ "VFrameFormat" "StandardCAN_FD";

BO_ 1920 BMS_BasicInfo: 16 test
 SG_ BMS_Vpack : 0|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_PackVoltage : 32|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_PackCurrent : 64|32@1- (1,0) [-2147483648|2147483647] "mA"  Project
 SG_ BMS_AvgCurrent : 96|32@1- (1,0) [-2147483648|2147483647] "mA"  Project

BO_ 1921 BMS_AllStatus: 32 test
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SafetyStatusLow : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SafetyStatusHigh : 80|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_WarnStatus : 96|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_STStatus : 112|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PFStatusLow : 128|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PFStatusHigh : 144|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CBS0_15 : 160|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CycleCount : 176|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_LearnCycle : 192|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_EngMode : 208|8@1+ (1,0) [0|255] "Hex"  Project
 SG_ BMS_SystemTime : 216|32@1+ (1,0) [0|4294967295] "DateTime"  Project
 SG_ BMS_UserRC : 248|16@1+ (1,0) [0|65535] "mAh"  Project

BO_ 1922 BMS_AllTemps: 96 test
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp5 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp6 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp7 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp8 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp9 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp10 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp11 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp12 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp13 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp14 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp15 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp16 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp17 : 256|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp18 : 272|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp19 : 288|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp20 : 304|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp21 : 320|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp22 : 336|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp23 : 352|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp24 : 368|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp25 : 384|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp26 : 400|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp27 : 416|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp28 : 432|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp29 : 448|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp30 : 464|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp31 : 480|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp32 : 496|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp33 : 512|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp34 : 528|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp35 : 544|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp36 : 560|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp37 : 576|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp38 : 592|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp39 : 608|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp40 : 624|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp41 : 640|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp42 : 656|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp43 : 672|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp44 : 688|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp45 : 704|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp46 : 720|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp47 : 736|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp48 : 752|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project

BO_ 1923 BMS_AllCellVolts: 144 test
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt5 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt6 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt7 : 96|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt8 : 112|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt9 : 128|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt10 : 144|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt11 : 160|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt12 : 176|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt13 : 192|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt14 : 208|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt15 : 224|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt16 : 240|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt17 : 256|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt18 : 272|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt19 : 288|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt20 : 304|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt21 : 320|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt22 : 336|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt23 : 352|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt24 : 368|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt25 : 384|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt26 : 400|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt27 : 416|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt28 : 432|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt29 : 448|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt30 : 464|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt31 : 480|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt32 : 496|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt33 : 512|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt34 : 528|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt35 : 544|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt36 : 560|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt37 : 576|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt38 : 592|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt39 : 608|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt40 : 624|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt41 : 640|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt42 : 656|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt43 : 672|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt44 : 688|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt45 : 704|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt46 : 720|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt47 : 736|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt48 : 752|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt49 : 768|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt50 : 784|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt51 : 800|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt52 : 816|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt53 : 832|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt54 : 848|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt55 : 864|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt56 : 880|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt57 : 896|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt58 : 912|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt59 : 928|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt60 : 944|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt61 : 960|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt62 : 976|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt63 : 992|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt64 : 1008|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt65 : 1024|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt66 : 1040|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt67 : 1056|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt68 : 1072|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt69 : 1088|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt70 : 1104|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt71 : 1120|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt72 : 1136|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1924 BMS_ExtendedInfo: 32 test
 SG_ BMS_UserRSOC : 0|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_FCCmin : 8|16@1+ (1,0) [0|65535] "100mWh"  Project
 SG_ BMS_DeltaRC : 24|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_Vdfuse : 40|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_VchgPlus : 72|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_VpackPlus : 104|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_Vcfuse : 136|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_V_insulation : 168|32@1+ (1,0) [0|4294967295] "mV"  Project

BA_ "VFrameFormat" BO_ 1920 "StandardCAN_FD";
BA_ "VFrameFormat" BO_ 1921 "StandardCAN_FD";
BA_ "VFrameFormat" BO_ 1922 "StandardCAN_FD";
BA_ "VFrameFormat" BO_ 1923 "StandardCAN_FD";
BA_ "VFrameFormat" BO_ 1924 "StandardCAN_FD";
