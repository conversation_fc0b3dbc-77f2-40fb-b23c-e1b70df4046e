VERSION ""


NS_ :
        NS_DESC_
        CM_
        BA_DEF_
        BA_
        VAL_
        CAT_DEF_
        CAT_
        FILTER
        BA_DEF_DEF_
        EV_DATA_
        ENVVAR_DATA_
        SGTYPE_
        SGTYPE_VAL_
        BA_DEF_SGTYPE_
        BA_SGTYPE_
        SIG_TYPE_REF_
        VAL_TABLE_
        SIG_GROUP_
        SIG_VALTYPE_
        SIGTYPE_VALTYPE_
        BO_TX_BU_
        BA_DEF_REL_
        BA_REL_
        BA_DEF_DEF_REL_
        BU_SG_REL_
        BU_EV_REL_
        BU_BO_REL_
        SG_MUL_VAL_

BS_:

BU_: Project test

BO_ 1875 BMS: 8 test
 SG_ BMS_command M : 32|16@1+ (1,0) [0|65535] ""  Project
 SG_ BMS_Vpack m1 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_PackVoltage m2 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_PackCurrent m3 : 48|32@1- (1,0) [-2147483648|2147483647] "mA"  Project
 SG_ BMS_AvgCurrent m4 : 48|32@1- (1,0) [-2147483648|2147483647] "mA"  Project
 SG_ BMS_RSOC m5 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "%"  Project
 SG_ BMS_ASOC m6 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "%"  Project
 SG_ BMS_RC m7 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_FCC m8 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_DCR m9 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_FDCR m10 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_CycleCount m11 : 48|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_LearnCycle m12 : 48|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_UserRC m13 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_UserRSOC m14 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "%"  Project
 SG_ BMS_FCCmin m15 : 48|16@1+ (1,0) [0|65535] "100mWh"  Project
 SG_ BMS_DeltaRC m16 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_SrartRSOC m17 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "%"  Project
 SG_ BMS_StartFDCR m18 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_RCminCutoff m19 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_StartRSOCmin m20 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "%"  Project
 SG_ BMS_UsageCapacity m21 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_SuccChaCap m22 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mAh"  Project
 SG_ BMS_BatteryStatusLow m32 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_BatteryStatusHigh m32 : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusLow m33 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusHigh m33 : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SafetyStatusLow m34 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SafetyStatusHigh m34 : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_WarnStatus m35 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PFStatusLow m36 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PFStatusHigh m36 : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CBS0_15 m37 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_STStatus m43 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_Temp1 m48 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp2 m49 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp3 m50 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp4 m51 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp5 m52 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp6 m53 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp7 m54 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp8 m55 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp9 m56 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp10 m57 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp11 m58 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp12 m59 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_CellVolt1 m64 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt2 m65 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt3 m66 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt4 m67 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt5 m68 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt6 m69 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt7 m70 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt8 m71 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt9 m72 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt10 m73 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt11 m74 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt12 m75 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt13 m76 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt14 m77 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt15 m78 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt16 m79 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_EngMode m80 : 48|8@1+ (1,0) [0|255] "Hex"  Project
 SG_ BMS_SIM_PackCurrent m81 : 48|32@1- (1,0) [-2147483648|2147483647] "mA"  Project
 SG_ BMS_SIM_CellVolt1 m82 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt2 m82 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt3 m82 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt4 m83 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt5 m83 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt6 m83 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt7 m84 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt8 m84 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt9 m84 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt10 m85 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt11 m85 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt12 m85 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt13 m86 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt14 m86 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt15 m86 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt16 m87 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt17 m87 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt18 m87 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_Temp1 m88 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp2 m88 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp3 m88 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SystemTime m89 : 48|32@1+ (1,0) [0|4294967295] "DateTime"  Project
 SG_ BMS_SIM_Temp4 m90 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp5 m90 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Vbat m91 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_SIM_Vpack m92 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_SIM_MOSTemp m93 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_STStatus m94 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_HS_Current m95 : 48|32@1- (1,0) [-2147483648|2147483647] "mA"  Project
 SG_ BMS_ClientRC m96 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mWh"  Project
 SG_ BMS_ClientRSOC m97 : 48|16@1+ (1,0) [0|65535] "%"  Project
 SG_ BMS_ClientASOC m98 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_RC_mWh m99 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "mWh"  Project
 SG_ BMS_RSOC_mWh m100 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_ASOC_mWh m101 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_MaxError_bq m96 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_Voltage_bq m97 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_Current_bq m98 : 48|16@1- (1,0) [-32768|32767] "mA"  Project
 SG_ BMS_AvgCurrent_bq m99 : 48|16@1- (1,0) [-32768|32767] "mA"  Project
 SG_ BMS_RSOC_bq m100 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_RC_bq m101 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_FCC_bq m102 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_Temp_bq m103 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_CtlStatusFlgs_bq m104 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_Flags_bq m105 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_FlagsB_bq m106 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PassedCharge_bq m107 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_DOD0Time_bq m108 : 48|16@1+ (1,0) [0|65535] "DateTime"  Project
 SG_ BMS_RC_mWh_bq m109 : 48|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_AveragePower_bq m110 : 48|16@1+ (1,0) [0|65535] "mW"  Project
 SG_ BMS_CycleCount_bq m111 : 48|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_StateOfHealth_bq m112 : 48|16@1+ (1,0) [0|65535] "%"  Project
 SG_ BMS_ChgVoltage_bq m113 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_ChgCurrent_bq m114 : 48|16@1+ (1,0) [0|65535] "mA"  Project
 SG_ BMS_FCC_mWH_bq m115 : 48|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_PackConfig_bq m116 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_DC_bq m117 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_GridNum_bq m118 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_LearnedStatus_bq m119 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_DodAtEoc_bq m120 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_Qstart_bq m121 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_TrueRC_bq m122 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_TrueFCC_bq m123 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_StateTime_bq m124 : 48|16@1+ (1,0) [0|65535] "Sec"  Project
 SG_ BMS_QMaxPassedQ_bq m125 : 48|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_DOD0_bq m126 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_QMaxDod0_bq m127 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_OCVUpdateCount m128 : 48|32@1+ (0.05,0) [0|214748364.75] "Sec"  Project
 SG_ BMS_QMaxTime_bq m129 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_ReadDivider_bq m129 : 64|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_FunctionFlag_bq m130 : 48|8@1+ (1,0) [0|255] "Hex"  Project
 SG_ BMS_DividerTEST_bq m131 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CaliTEST_bq m132 : 48|32@1+ (1,0) [0|4294967295] "Hex"  Project
 SG_ BMS_CaliDvTEST_bq m133 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CaliStatus_bq m134 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SOCVolt1 m144 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt2 m145 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt3 m146 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt4 m147 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt5 m148 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt6 m149 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt7 m150 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt8 m151 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt9 m152 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt10 m153 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt11 m154 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt12 m155 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt13 m156 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt14 m157 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt15 m158 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOCVolt16 m159 : 48|32@1+ (1,0) [0|4294967295] "mAs"  Project
 SG_ BMS_SOC_State m160 : 48|8@1+ (1,0) [0|255] "Hex"  Project
 SG_ BMS_SMCB_Flag m161 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqVoltage m176 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_bqCurrent m177 : 48|16@1- (1,0) [-32768|32767] "mA"  Project
 SG_ BMS_bqSOC m178 : 48|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_bqRC m179 : 48|16@1+ (4,0) [0|262140] "mAh"  Project
 SG_ BMS_bqFCC m180 : 48|16@1+ (4,0) [0|262140] "mAh"  Project
 SG_ BMS_bqTemp m181 : 48|16@1- (0.1,0) [-3276.8|3276.7] "C"  Project
 SG_ BMS_bqCtlStatus m182 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqFlags m183 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqFlagsB m184 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqAvailableEngy m185 : 48|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_bqCycleCnt m186 : 48|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_bqSOH m187 : 48|16@1+ (1,0) [0|65535] "%"  Project
 SG_ BMS_bqChgVolt m188 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_bqFCC_cWH m189 : 48|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_bqPackConfig m190 : 48|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqDesignCapacity m191 : 48|16@1+ (4,0) [0|262140] "mAh"  Project
 SG_ BMS_PublicVer m240 : 48|24@1+ (1,0) [0|16777215] "Ver"  Project
 SG_ BMS_ChkSum m39 : 48|32@1+ (1,0) [0|4294967295] "Hex"  Project
 SG_ BMS_CanloaderChkSum m40 : 48|32@1+ (1,0) [0|4294967295] "Hex"  Project
 SG_ BMS_CellVolt17 m256 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt18 m257 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt19 m258 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt20 m259 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt21 m260 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt22 m261 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt23 m262 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt24 m263 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt25 m264 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt26 m265 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt27 m266 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt28 m267 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt29 m268 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt30 m269 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt31 m270 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt32 m271 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt33 m272 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt34 m273 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt35 m274 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt36 m275 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt37 m310 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt38 m311 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt39 m312 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt40 m313 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt41 m314 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt42 m315 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt43 m316 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt44 m317 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt45 m318 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt46 m319 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt47 m320 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt48 m321 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt49 m322 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt50 m323 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt51 m324 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt52 m325 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt53 m326 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt54 m327 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt55 m328 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt56 m329 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt57 m330 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt58 m331 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt59 m332 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt60 m333 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt61 m334 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt62 m335 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt63 m336 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt64 m337 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt65 m338 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt66 m339 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt67 m340 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt68 m341 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt69 m342 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt70 m343 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt71 m344 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt72 m345 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt19 m512 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt20 m512 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt21 m512 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt22 m513 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt23 m513 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt24 m513 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt25 m514 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt26 m514 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt27 m514 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt28 m515 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt29 m515 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt30 m515 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt31 m516 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt32 m516 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt33 m516 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt34 m517 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt35 m517 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt36 m517 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt37 m518 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt38 m518 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt39 m518 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt40 m519 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt41 m519 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt42 m519 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt43 m520 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt44 m520 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt45 m520 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt46 m521 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt47 m521 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt48 m521 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt49 m522 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt50 m522 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt51 m522 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt52 m523 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt53 m523 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt54 m523 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt55 m524 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt56 m524 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt57 m524 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt58 m525 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt59 m525 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt60 m525 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt61 m526 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt62 m526 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt63 m526 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt64 m527 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt65 m527 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt66 m527 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt67 m528 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt68 m528 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt69 m528 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt70 m529 : 48|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt71 m529 : 64|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_CellVolt72 m529 : 80|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_SIM_Temp7 m768 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp8 m768 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp9 m768 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp10 m769 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp11 m769 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp12 m769 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp13 m770 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp14 m770 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp15 m770 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp16 m771 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp17 m771 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp18 m771 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp19 m772 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp20 m772 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp21 m772 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp22 m773 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp23 m773 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp24 m773 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp25 m774 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp26 m774 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp27 m774 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp28 m775 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp29 m775 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp30 m775 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp31 m776 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp32 m776 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp33 m776 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp34 m777 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp35 m777 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp36 m777 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp37 m778 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp38 m778 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp39 m778 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp40 m779 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp41 m779 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp42 m779 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp43 m780 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp44 m780 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp45 m780 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp46 m781 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp47 m781 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_SIM_Temp48 m781 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp13 m346 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp14 m347 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp15 m348 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp16 m349 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp17 m350 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp18 m351 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp19 m352 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp20 m353 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp21 m354 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp22 m355 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp23 m356 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp24 m357 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp25 m358 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp26 m359 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp27 m360 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp28 m361 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp29 m362 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp30 m363 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp31 m364 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp32 m365 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp33 m366 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp34 m367 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp35 m368 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp36 m369 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp37 m370 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp38 m371 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp39 m372 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp40 m373 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp41 m374 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp42 m375 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp43 m376 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp44 m377 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp45 m378 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp46 m379 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp47 m380 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp48 m381 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Vdfuse m1024 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_VchgPlus m1025 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_VpackPlus m1026 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_V_insulation m1027 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_Vcfuse m1028 : 48|32@1+ (1,0) [0|4294967295] "mV"  Project
 SG_ BMS_InsulResWarnValue m608 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "Kohm"  Project
 SG_ BMS_InsulResSafetyValue m609 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
 SG_ BMS_InsulResN_1996 m610 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
 SG_ BMS_AvgInsulResP m611 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
 SG_ BMS_AvgInsulResN m612 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
 SG_ BMS_InsulResP m613 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
 SG_ BMS_InsulResN m614 : 48|32@1- (1,0) [-3.4E+38|3.4E+38] "KOhm"  Project
