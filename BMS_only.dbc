VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS HOST

BO_ 2147484160 BMS_BasicInfo: 64 BMS
 SG_ BMS_Vpack : 0|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_PackVoltage : 32|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_PackCurrent : 64|32@1- (1,0) [-2147483648|2147483647] "mA" HOST
 SG_ BMS_AvgCurrent : 96|32@1- (1,0) [-2147483648|2147483647] "mA" HOST
 SG_ BMS_RSOC : 128|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ BMS_ASOC : 144|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ BMS_RC : 160|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_FCC : 192|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_CycleCount : 224|16@1+ (1,0) [0|65535] "Times" HOST
 SG_ BMS_LearnCycle : 240|16@1+ (1,0) [0|65535] "Times" HOST
 SG_ BMS_UserRC : 256|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_DCR : 288|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_FDCR : 320|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_UserRSOC : 352|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ BMS_FCCmin : 368|32@1+ (1,0) [0|4294967295] "100mWh" HOST
 SG_ BMS_DeltaRC : 400|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_SrartRSOC : 432|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ BMS_StartFDCR : 448|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_RCminCutoff : 480|32@1+ (1,0) [0|4294967295] "mAh" HOST

BO_ 2147484161 BMS_AllStatus: 64 BMS
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_SafetyStatusLow : 64|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_SafetyStatusHigh : 80|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_WarnStatus : 96|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_STStatus : 112|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_PFStatusLow : 128|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_PFStatusHigh : 144|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_CBS0_15 : 160|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_EngMode : 176|8@1+ (1,0) [0|255] "Hex" HOST
 SG_ BMS_SystemTime : 184|32@1+ (1,0) [0|4294967295] "DateTime" HOST
 SG_ BMS_StartRSOCmin : 216|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ BMS_UsageCapacity : 232|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ BMS_SuccChaCap : 264|32@1+ (1,0) [0|4294967295] "mAh" HOST

BO_ 2147484162 BMS_AllTemps: 64 BMS
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp5 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp6 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp7 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp8 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp9 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp10 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp11 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp12 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp13 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp14 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp15 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp16 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp17 : 256|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp18 : 272|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp19 : 288|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp20 : 304|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp21 : 320|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp22 : 336|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp23 : 352|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp24 : 368|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp25 : 384|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp26 : 400|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp27 : 416|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp28 : 432|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp29 : 448|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp30 : 464|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp31 : 480|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp32 : 496|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST

BO_ 2147484169 BMS_AllTemps2: 32 BMS
 SG_ BMS_Temp33 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp34 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp35 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp36 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp37 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp38 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp39 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp40 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp41 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp42 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp43 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp44 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp45 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp46 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp47 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST
 SG_ BMS_Temp48 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC" HOST

BO_ 2147484163 BMS_CellVolts1: 64 BMS
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt5 : 64|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt6 : 80|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt7 : 96|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt8 : 112|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt9 : 128|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt10 : 144|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt11 : 160|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt12 : 176|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt13 : 192|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt14 : 208|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt15 : 224|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt16 : 240|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt17 : 256|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt18 : 272|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt19 : 288|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt20 : 304|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt21 : 320|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt22 : 336|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt23 : 352|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt24 : 368|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt25 : 384|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt26 : 400|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt27 : 416|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt28 : 432|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt29 : 448|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt30 : 464|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt31 : 480|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt32 : 496|16@1+ (1,0) [0|65535] "mV" HOST

BO_ 2147484164 BMS_CellVolts2: 64 BMS
 SG_ BMS_CellVolt33 : 0|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt34 : 16|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt35 : 32|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt36 : 48|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt37 : 64|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt38 : 80|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt39 : 96|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt40 : 112|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt41 : 128|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt42 : 144|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt43 : 160|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt44 : 176|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt45 : 192|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt46 : 208|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt47 : 224|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt48 : 240|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt49 : 256|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt50 : 272|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt51 : 288|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt52 : 304|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt53 : 320|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt54 : 336|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt55 : 352|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt56 : 368|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt57 : 384|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt58 : 400|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt59 : 416|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt60 : 432|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt61 : 448|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt62 : 464|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt63 : 480|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt64 : 496|16@1+ (1,0) [0|65535] "mV" HOST

BO_ 2147484165 BMS_CellVolts3: 32 BMS
 SG_ BMS_CellVolt65 : 0|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt66 : 16|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt67 : 32|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt68 : 48|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt69 : 64|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt70 : 80|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt71 : 96|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_CellVolt72 : 112|16@1+ (1,0) [0|65535] "mV" HOST

BO_ 2147484166 BMS_Voltages: 32 BMS
 SG_ BMS_Vdfuse : 0|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_VchgPlus : 32|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_VpackPlus : 64|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_Vcfuse : 96|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_V_insulation : 128|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ BMS_InsulResP : 160|32@1+ (1,0) [0|4294967295] "KOhm" HOST
 SG_ BMS_InsulResN : 192|32@1+ (1,0) [0|4294967295] "KOhm" HOST
 SG_ BMS_AvgInsulResP : 224|32@1+ (1,0) [0|4294967295] "KOhm" HOST

BO_ 2147484167 BMS_bqGauge: 32 BMS
 SG_ BMS_bqVoltage : 0|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ BMS_bqCurrent : 16|16@1- (1,0) [-32768|32767] "mA" HOST
 SG_ BMS_bqSOC : 32|8@1+ (1,0) [0|255] "%" HOST
 SG_ BMS_bqRC : 40|16@1+ (4,0) [0|262140] "mAh" HOST
 SG_ BMS_bqFCC : 56|16@1+ (4,0) [0|262140] "mAh" HOST
 SG_ BMS_bqTemp : 72|16@1- (0.1,0) [-3276.8|3276.7] "C" HOST
 SG_ BMS_bqCycleCnt : 88|16@1+ (1,0) [0|65535] "Times" HOST
 SG_ BMS_bqSOH : 104|16@1+ (1,0) [0|65535] "%" HOST
 SG_ BMS_bqCtlStatus : 120|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_bqFlags : 136|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_bqFlagsB : 152|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_bqPackConfig : 168|16@1+ (1,0) [0|65535] "Hex" HOST
 SG_ BMS_bqAvailableEngy : 184|16@1+ (560,0) [0|36699600] "mWh" HOST
 SG_ BMS_bqFCC_cWH : 200|16@1+ (560,0) [0|36699600] "mWh" HOST
 SG_ BMS_bqDesignCapacity : 216|16@1+ (4,0) [0|262140] "mAh" HOST
 SG_ BMS_bqChgVolt : 232|16@1+ (1,0) [0|65535] "mV" HOST

BO_ 2147484168 BMS_Version: 8 BMS
 SG_ BMS_PublicVer : 0|24@1+ (1,0) [0|16777215] "Ver" HOST
 SG_ BMS_ChkSum : 24|32@1+ (1,0) [0|4294967295] "Hex" HOST

BA_DEF_ BO_ "CANFD_BRS" ENUM "0","1";
BA_DEF_ BO_ "VFrameFormat" ENUM "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ "DBName" STRING ;
BA_DEF_ "Baudrate" INT 0 1000000;
BA_DEF_DEF_ "CANFD_BRS" "1";
BA_DEF_DEF_ "VFrameFormat" "ExtendedCAN_FD";
BA_DEF_DEF_ "DBName" "BMS_Extended_CANFD";
BA_DEF_DEF_ "Baudrate" 500000;

BA_ "CANFD_BRS" BO_ 2147484160 1;
BA_ "VFrameFormat" BO_ 2147484160 15;
BA_ "CANFD_BRS" BO_ 2147484161 1;
BA_ "VFrameFormat" BO_ 2147484161 15;
BA_ "CANFD_BRS" BO_ 2147484162 1;
BA_ "VFrameFormat" BO_ 2147484162 15;
BA_ "CANFD_BRS" BO_ 2147484163 1;
BA_ "VFrameFormat" BO_ 2147484163 15;
BA_ "CANFD_BRS" BO_ 2147484164 1;
BA_ "VFrameFormat" BO_ 2147484164 15;
BA_ "CANFD_BRS" BO_ 2147484165 1;
BA_ "VFrameFormat" BO_ 2147484165 15;
BA_ "CANFD_BRS" BO_ 2147484166 1;
BA_ "VFrameFormat" BO_ 2147484166 15;
BA_ "CANFD_BRS" BO_ 2147484167 1;
BA_ "VFrameFormat" BO_ 2147484167 15;
BA_ "VFrameFormat" BO_ 2147484168 1;
BA_ "CANFD_BRS" BO_ 2147484169 1;
BA_ "VFrameFormat" BO_ 2147484169 15;

