//*******************************************************
//  * Name    :BMS_only.dbc
//  * Author  :<PERSON><PERSON>reat @WangHu
//  * Function:****
//  * Version :V1.0.0
//  * Data    :2025.07.04 10:08:11
//*******************************************************

VERSION "WangHu V1.0"


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: test Project

BO_ 1875 BMS_BasicInfo: 8 test
 SG_ BMS_Vpack : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_PackVoltage : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_PackCurrent : 48|16@1- (1,0) [-32768|32767] "mA" Project

BO_ 1876 BMS_Status: 8 test
 SG_ BMS_AvgCurrent : 0|16@1- (1,0) [-32768|32767] "mA" Project
 SG_ BMS_RSOC : 16|8@1+ (1,0) [0|255] "%" Project
 SG_ BMS_ASOC : 24|8@1+ (1,0) [0|255] "%" Project
 SG_ BMS_EngMode : 32|8@1+ (1,0) [0|255] "Hex" Project
 SG_ BMS_SystemTime : 40|24@1+ (1,0) [0|16777215] "DateTime" Project

BO_ 1877 BMS_Capacity: 8 test
 SG_ BMS_RC : 0|16@1+ (1,0) [0|65535] "mAh" Project
 SG_ BMS_FCC : 16|16@1+ (1,0) [0|65535] "mAh" Project
 SG_ BMS_CycleCount : 32|16@1+ (1,0) [0|65535] "Times" Project
 SG_ BMS_LearnCycle : 48|16@1+ (1,0) [0|65535] "Times" Project

BO_ 1878 BMS_AdvCapacity: 8 test
 SG_ BMS_UserRC : 0|16@1+ (1,0) [0|65535] "mAh" Project
 SG_ BMS_UserRSOC : 16|8@1+ (1,0) [0|255] "%" Project
 SG_ BMS_FCCmin : 24|16@1+ (1,0) [0|65535] "100mWh" Project
 SG_ BMS_DeltaRC : 40|16@1+ (1,0) [0|65535] "mAh" Project

BO_ 1879 BMS_StatusFlags: 8 test
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex" Project

BO_ 1880 BMS_SafetyStatus: 8 test
 SG_ BMS_SafetyStatusLow : 0|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_SafetyStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_WarnStatus : 32|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_STStatus : 48|16@1+ (1,0) [0|65535] "Hex" Project

BO_ 1881 BMS_ProtectionStatus: 8 test
 SG_ BMS_PFStatusLow : 0|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_PFStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex" Project
 SG_ BMS_CBS0_15 : 32|16@1+ (1,0) [0|65535] "Hex" Project

BO_ 1882 BMS_Temp1_4: 8 test
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project

BO_ 1883 BMS_Temp5_8: 8 test
 SG_ BMS_Temp5 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp6 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp7 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp8 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project

BO_ 1884 BMS_Temp9_12: 8 test
 SG_ BMS_Temp9 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp10 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp11 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project
 SG_ BMS_Temp12 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC" Project

BO_ 1885 BMS_CellVolt1_4: 8 test
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV" Project

BO_ 1886 BMS_CellVolt5_8: 8 test
 SG_ BMS_CellVolt5 : 0|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt6 : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt7 : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt8 : 48|16@1+ (1,0) [0|65535] "mV" Project

BO_ 1887 BMS_CellVolt9_12: 8 test
 SG_ BMS_CellVolt9 : 0|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt10 : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt11 : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt12 : 48|16@1+ (1,0) [0|65535] "mV" Project

BO_ 1888 BMS_CellVolt13_16: 8 test
 SG_ BMS_CellVolt13 : 0|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt14 : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt15 : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_CellVolt16 : 48|16@1+ (1,0) [0|65535] "mV" Project

BO_ 1894 BMS_Voltages: 8 test
 SG_ BMS_Vdfuse : 0|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_VchgPlus : 16|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_VpackPlus : 32|16@1+ (1,0) [0|65535] "mV" Project
 SG_ BMS_Vcfuse : 48|16@1+ (1,0) [0|65535] "mV" Project

BO_ 1896 BMS_Version: 8 test
 SG_ BMS_PublicVer : 0|24@1+ (1,0) [0|16777215] "Ver" Project
 SG_ BMS_ChkSum : 24|32@1+ (1,0) [0|4294967295] "Hex" Project



BA_DEF_ EV_  "GenEnvIsGeneratedDsp" ENUM  "No","Yes";
BA_DEF_ EV_  "GenEnvAutoGenCtrl" ENUM  "No","Yes";
BA_DEF_ EV_  "GenEnvMsgOffset" INT 0 2147483647;
BA_DEF_ EV_  "GenEnvMsgName" STRING ;
BA_DEF_ EV_  "GenEnvIsGeneratedSnd" ENUM  "No","Yes";
BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 1215752192;
BA_DEF_ SG_  "GenSigTimeoutValue" FLOAT 0 1215752192;
BA_DEF_ SG_  "GenSigInactiveValue" FLOAT 0 1215752192;
BA_DEF_ SG_  "GenSigMinValue" INT 0 2147483647;
BA_DEF_ SG_  "GenSigMaxValue" INT 0 2147483647;
BA_DEF_ SG_  "GenSigSendType" ENUM ""; 
BA_DEF_ SG_  "GenSigEnvVarType" ENUM  "int","float","undef";
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_  "NmMessage" ENUM "No", "Yes"; 
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","J1939PG","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ BO_  "GenMsgSendType" ENUM ""; 
BA_DEF_ BU_  "NmStationAddress"  HEX 0 127;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "GenEnvVarPrefix" STRING ;
BA_DEF_  "GenEnvVarEndingSnd" STRING ;
BA_DEF_  "GenEnvVarEndingDsp" STRING ;
BA_DEF_  "DBName" STRING ;
BA_DEF_ BO_  "_Cdb_Sc_CTm_FAW_v3_0" INT 0 0;
BA_DEF_ BO_  "_Cdb_Sc_CTm_FAW_v2_3" INT 0 0;
BA_DEF_ BO_  "_Cdb_Sc_CTm_FAW_v3_1" INT 0 0;
BA_DEF_ BO_  "_Cdb_Sc_CTm_FAW_v2_2" INT 0 0;
BA_DEF_ BO_  "_Cdb_Sc_CTm_FAW_v2_1" INT 0 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmMessage" "No"; 
BA_DEF_DEF_  "GenEnvIsGeneratedDsp" "No";
BA_DEF_DEF_  "GenEnvAutoGenCtrl" "No";
BA_DEF_DEF_  "GenEnvMsgOffset" 0;
BA_DEF_DEF_  "GenEnvMsgName" "";
BA_DEF_DEF_  "GenEnvIsGeneratedSnd" "No";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigTimeoutValue" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigMinValue" 0;
BA_DEF_DEF_  "GenSigMaxValue" 0;
BA_DEF_DEF_  "GenSigSendType" "";
BA_DEF_DEF_  "GenSigEnvVarType" "undef"; 
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgSendType" "";
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "BusType" "CAN"; 
BA_DEF_DEF_  "GenEnvVarPrefix" "PE"; 
BA_DEF_DEF_  "GenEnvVarEndingSnd" "_Ps"; 
BA_DEF_DEF_  "GenEnvVarEndingDsp" "_Pd"; 
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "_Cdb_Sc_CTm_FAW_v3_0" 0;
BA_DEF_DEF_  "_Cdb_Sc_CTm_FAW_v2_3" 0;
BA_DEF_DEF_  "_Cdb_Sc_CTm_FAW_v3_1" 0;
BA_DEF_DEF_  "_Cdb_Sc_CTm_FAW_v2_2" 0;
BA_DEF_DEF_  "_Cdb_Sc_CTm_FAW_v2_1" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";

BA_ "DBName" "CanMatrix";
BA_ "GenSigMaxValue" SG_ 1875 BMS_Vpack 65535; 
BA_ "GenSigMaxValue" SG_ 1875 BMS_PackVoltage 65535; 
BA_ "GenSigMaxValue" SG_ 1875 BMS_PackCurrent 65535; 
BA_ "GenSigMaxValue" SG_ 1876 BMS_AvgCurrent 65535; 
BA_ "GenSigMaxValue" SG_ 1876 BMS_RSOC 255; 
BA_ "GenSigMaxValue" SG_ 1876 BMS_ASOC 255; 
BA_ "GenSigMaxValue" SG_ 1876 BMS_EngMode 255; 
BA_ "GenSigMaxValue" SG_ 1876 BMS_SystemTime 16777215; 
BA_ "GenSigMaxValue" SG_ 1877 BMS_RC 65535; 
BA_ "GenSigMaxValue" SG_ 1877 BMS_FCC 65535; 
BA_ "GenSigMaxValue" SG_ 1877 BMS_CycleCount 65535; 
BA_ "GenSigMaxValue" SG_ 1877 BMS_LearnCycle 65535; 
BA_ "GenSigMaxValue" SG_ 1878 BMS_UserRC 65535; 
BA_ "GenSigMaxValue" SG_ 1878 BMS_UserRSOC 255; 
BA_ "GenSigMaxValue" SG_ 1878 BMS_FCCmin 65535; 
BA_ "GenSigMaxValue" SG_ 1878 BMS_DeltaRC 65535; 
BA_ "GenSigMaxValue" SG_ 1879 BMS_BatteryStatusLow 65535; 
BA_ "GenSigMaxValue" SG_ 1879 BMS_BatteryStatusHigh 65535; 
BA_ "GenSigMaxValue" SG_ 1879 BMS_PackStatusLow 65535; 
BA_ "GenSigMaxValue" SG_ 1879 BMS_PackStatusHigh 65535; 
BA_ "GenSigMaxValue" SG_ 1880 BMS_SafetyStatusLow 65535; 
BA_ "GenSigMaxValue" SG_ 1880 BMS_SafetyStatusHigh 65535; 
BA_ "GenSigMaxValue" SG_ 1880 BMS_WarnStatus 65535; 
BA_ "GenSigMaxValue" SG_ 1880 BMS_STStatus 65535; 
BA_ "GenSigMaxValue" SG_ 1881 BMS_PFStatusLow 65535; 
BA_ "GenSigMaxValue" SG_ 1881 BMS_PFStatusHigh 65535; 
BA_ "GenSigMaxValue" SG_ 1881 BMS_CBS0_15 65535; 
BA_ "GenSigMaxValue" SG_ 1882 BMS_Temp1 65535; 
BA_ "GenSigMaxValue" SG_ 1882 BMS_Temp2 65535; 
BA_ "GenSigMaxValue" SG_ 1882 BMS_Temp3 65535; 
BA_ "GenSigMaxValue" SG_ 1882 BMS_Temp4 65535; 
BA_ "GenSigMaxValue" SG_ 1883 BMS_Temp5 65535; 
BA_ "GenSigMaxValue" SG_ 1883 BMS_Temp6 65535; 
BA_ "GenSigMaxValue" SG_ 1883 BMS_Temp7 65535; 
BA_ "GenSigMaxValue" SG_ 1883 BMS_Temp8 65535; 
BA_ "GenSigMaxValue" SG_ 1884 BMS_Temp9 65535; 
BA_ "GenSigMaxValue" SG_ 1884 BMS_Temp10 65535; 
BA_ "GenSigMaxValue" SG_ 1884 BMS_Temp11 65535; 
BA_ "GenSigMaxValue" SG_ 1884 BMS_Temp12 65535; 
BA_ "GenSigMaxValue" SG_ 1885 BMS_CellVolt1 65535; 
BA_ "GenSigMaxValue" SG_ 1885 BMS_CellVolt2 65535; 
BA_ "GenSigMaxValue" SG_ 1885 BMS_CellVolt3 65535; 
BA_ "GenSigMaxValue" SG_ 1885 BMS_CellVolt4 65535; 
BA_ "GenSigMaxValue" SG_ 1886 BMS_CellVolt5 65535; 
BA_ "GenSigMaxValue" SG_ 1886 BMS_CellVolt6 65535; 
BA_ "GenSigMaxValue" SG_ 1886 BMS_CellVolt7 65535; 
BA_ "GenSigMaxValue" SG_ 1886 BMS_CellVolt8 65535; 
BA_ "GenSigMaxValue" SG_ 1887 BMS_CellVolt9 65535; 
BA_ "GenSigMaxValue" SG_ 1887 BMS_CellVolt10 65535; 
BA_ "GenSigMaxValue" SG_ 1887 BMS_CellVolt11 65535; 
BA_ "GenSigMaxValue" SG_ 1887 BMS_CellVolt12 65535; 
BA_ "GenSigMaxValue" SG_ 1888 BMS_CellVolt13 65535; 
BA_ "GenSigMaxValue" SG_ 1888 BMS_CellVolt14 65535; 
BA_ "GenSigMaxValue" SG_ 1888 BMS_CellVolt15 65535; 
BA_ "GenSigMaxValue" SG_ 1888 BMS_CellVolt16 65535; 
BA_ "GenSigMaxValue" SG_ 1894 BMS_Vdfuse 65535; 
BA_ "GenSigMaxValue" SG_ 1894 BMS_VchgPlus 65535; 
BA_ "GenSigMaxValue" SG_ 1894 BMS_VpackPlus 65535; 
BA_ "GenSigMaxValue" SG_ 1894 BMS_Vcfuse 65535; 
BA_ "GenSigMaxValue" SG_ 1896 BMS_PublicVer 16777215; 
BA_ "GenSigMaxValue" SG_ 1896 BMS_ChkSum 2147483647; 



