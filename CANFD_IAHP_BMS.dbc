VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS HOST


BO_ 2147485452 BMS_PackInfo: 64 BMS
 SG_ BMS_Vpack : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_PackVoltage : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_PackCurrent : 64|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST
 SG_ BMS_AvgCurrent : 96|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST
 SG_ BMS_RSOC : 128|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_ASOC : 144|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_RC : 160|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_FCC : 192|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_CycleCount : 224|16@1+ (1,0) [0|65535] "Times"  HOST
 SG_ BMS_LearnCycle : 240|16@1+ (1,0) [0|65535] "Times"  HOST
 SG_ BMS_UserRC : 256|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_DCR : 288|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_FDCR : 320|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_UserRSOC : 352|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_FCCmin : 368|32@1+ (1,0) [0|4294967295] "100mWh"  HOST
 SG_ BMS_DeltaRC : 400|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SrartRSOC : 432|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_StartFDCR : 448|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_RCminCutoff : 480|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147485453 BMS_Status: 64 BMS
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_SafetyStatusLow : 64|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_SafetyStatusHigh : 80|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_WarnStatus : 96|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_STStatus : 112|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PFStatusLow : 128|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PFStatusHigh : 144|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_CBS0_15 : 160|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_StartRSOCmin : 176|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_UsageCapacity : 192|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SuccChaCap : 224|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SystemTime : 256|32@1+ (1,0) [0|4294967295] "DateTime"  HOST
 SG_ BMS_EngMode : 288|8@1+ (1,0) [0|255] "Hex"  HOST

BO_ 2147485454 BMS_Temperatures1: 64 BMS
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp5 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp6 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp7 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp8 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp9 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp10 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp11 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp12 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp13 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp14 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp15 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp16 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp17 : 256|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp18 : 272|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp19 : 288|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp20 : 304|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp21 : 320|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp22 : 336|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp23 : 352|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp24 : 368|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp25 : 384|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp26 : 400|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp27 : 416|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp28 : 432|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp29 : 448|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp30 : 464|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp31 : 480|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp32 : 496|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147485455 BMS_Temperatures2: 32 BMS
 SG_ BMS_Temp33 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp34 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp35 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp36 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp37 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp38 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp39 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp40 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp41 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp42 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp43 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp44 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp45 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp46 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp47 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp48 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147485456 BMS_CellVoltages1: 64 BMS
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt5 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt6 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt7 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt8 : 112|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt9 : 128|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt10 : 144|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt11 : 160|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt12 : 176|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt13 : 192|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt14 : 208|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt15 : 224|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt16 : 240|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt17 : 256|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt18 : 272|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt19 : 288|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt20 : 304|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt21 : 320|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt22 : 336|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt23 : 352|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt24 : 368|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt25 : 384|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt26 : 400|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt27 : 416|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt28 : 432|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt29 : 448|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt30 : 464|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt31 : 480|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt32 : 496|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485457 BMS_CellVoltages2: 64 BMS
 SG_ BMS_CellVolt33 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt34 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt35 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt36 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt37 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt38 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt39 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt40 : 112|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt41 : 128|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt42 : 144|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt43 : 160|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt44 : 176|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt45 : 192|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt46 : 208|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt47 : 224|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt48 : 240|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt49 : 256|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt50 : 272|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt51 : 288|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt52 : 304|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt53 : 320|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt54 : 336|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt55 : 352|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt56 : 368|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt57 : 384|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt58 : 400|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt59 : 416|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt60 : 432|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt61 : 448|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt62 : 464|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt63 : 480|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt64 : 496|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485458 BMS_CellVoltages3: 32 BMS
 SG_ BMS_CellVolt65 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt66 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt67 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt68 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt69 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt70 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt71 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt72 : 112|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485459 BMS_OtherVoltages: 32 BMS
 SG_ BMS_Vdfuse : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_VchgPlus : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_VpackPlus : 64|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_Vcfuse : 96|32@1+ (1,0) [0|4294967295] "mV"  HOST

BO_ 2147485460 BMS_Version: 8 BMS
 SG_ BMS_ChkSum : 0|32@1+ (1,0) [0|4294967295] "Hex"  HOST
 SG_ BMS_PublicVer : 32|24@1+ (1,0) [0|16777215] "Ver"  HOST


BA_DEF_  "MultiplexExtEnabled" ENUM  "No","Yes";
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_  "MultiplexExtEnabled" "No";
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "BBU_CANFD";
BA_ "CANFD_BRS" BO_ 2147485452 1;
BA_ "VFrameFormat" BO_ 2147485452 15;
BA_ "CANFD_BRS" BO_ 2147485453 1;
BA_ "VFrameFormat" BO_ 2147485453 15;
BA_ "CANFD_BRS" BO_ 2147485454 1;
BA_ "VFrameFormat" BO_ 2147485454 15;
BA_ "CANFD_BRS" BO_ 2147485455 1;
BA_ "VFrameFormat" BO_ 2147485455 15;
BA_ "CANFD_BRS" BO_ 2147485456 1;
BA_ "VFrameFormat" BO_ 2147485456 15;
BA_ "CANFD_BRS" BO_ 2147485457 1;
BA_ "VFrameFormat" BO_ 2147485457 15;
BA_ "CANFD_BRS" BO_ 2147485458 1;
BA_ "VFrameFormat" BO_ 2147485458 15;
BA_ "CANFD_BRS" BO_ 2147485459 1;
BA_ "VFrameFormat" BO_ 2147485459 15;
BA_ "VFrameFormat" BO_ 2147485460 1;

