VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS_MV RTE EMB_SSW
VAL_TABLE_ Vtsig_NMH_State 2 "NMH_state_prepare_bus_sleep" 1 "NMH_state_ready_sleep" 0 "NMH_state_bus_sleep" ;
VAL_TABLE_ VTb_Calibration_status 7 "SNA" 6 "INIT" 5 "Invalid_Calibration_request" 4 "Calibration_sequence_error" 3 "Temperature_Mode_active" 2 "Voltage_Mode_active" 1 "Current_Mode_active" 0 "Calibration_inactive" ;
VAL_TABLE_ VTb_CurrentCal_state 15 "SNA" 14 "INIT" 13 "CCAL_state_error" 12 "CCAL_state_duration_error" 11 "CCAL_state_Invalid_req" 10 "CCAL_state_seq_error" 9 "CCAL_state_finished" 8 "CCAL_state_storing" 7 "CCAL_state_calculating" 6 "CCAL_state_high_cal_done" 5 "CCAL_state_high_cal_active" 4 "CCAL_state_high_cal_init" 3 "CCAL_state_low_cal_done" 2 "CCAL_state_low_cal_active" 1 "CCAL_state_low_cal_init" 0 "CCAL_state_off" ;
VAL_TABLE_ VTb_VoltageCal_state 15 "SNA" 14 "INIT" 13 "VCAL_state_error" 12 "VCAL_state_duration_error" 11 "VCAL_state_Invalid_req" 10 "VCAL_state_seq_error" 9 "VCAL_state_finished" 8 "VCAL_state_storing" 7 "VCAL_state_calculating" 6 "VCAL_state_high_cal_done" 5 "VCAL_state_high_cal_active" 4 "VCAL_state_high_cal_init" 3 "VCAL_state_low_cal_done" 2 "VCAL_state_low_cal_active" 1 "VCAL_state_low_cal_init" 0 "VCAL_state_off" ;
VAL_TABLE_ VTb_TemperatureCal_state 15 "SNA" 14 "INIT" 13 "Invalid" 12 "Invalid" 11 "Invalid" 10 "TCAL_state_error" 9 "TCAL_state_duration_error" 8 "TCAL_state_Invalid_req" 7 "TCAL_state_seq_error" 6 "TCAL_state_finished" 5 "TCAL_state_storing" 4 "TCAL_state_calculating" 3 "TCAL_state_cal_done" 2 "TCAL_state_cal_active" 1 "TCAL_state_cal_init" 0 "TCAL_state_off" ;
VAL_TABLE_ VTb_Calibration_request 7 "SNA" 6 "Invalid" 5 "Invalid" 4 "Invalid" 3 "Temperature_Mode_requested" 2 "Voltage_Mode_requested" 1 "Current_Mode_requested" 0 "No_Calibration_requested" ;
VAL_TABLE_ VTb_CurrentCal_state_request 7 "SNA" 6 "Invalid" 5 "CCAL_state_req_exit" 4 "CCAL_state_req_high_cal" 3 "CCAL_state_req_init_high_cal" 2 "CCAL_state_req_low_cal" 1 "CCAL_state_req_init_low_cal" 0 "CCAL_state_req_off" ;
VAL_TABLE_ VTb_VoltageCal_state_request 7 "SNA" 6 "Invalid" 5 "VCAL_state_req_exit" 4 "VCAL_state_req_high_cal" 3 "VCAL_state_req_init_high_cal" 2 "VCAL_state_req_low_cal" 1 "VCAL_state_req_init_low_cal" 0 "VCAL_state_req_off" ;
VAL_TABLE_ VTb_TemperatureCal_state_request 7 "SNA" 6 "Invalid" 5 "Invalid" 4 "Invalid" 3 "TCAL_state_req_exit" 2 "TCAL_state_req_cal" 1 "TCAL_state_req_init_cal" 0 "TCAL_state_req_off" ;


BO_ 2645185858 TP_DBG_GDRV: 64 BMS_MV
 SG_ ERR_CFet_ErrDigOut : 200|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrCurSnsAmp3 : 192|8@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrCurSnsAmp12 : 184|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrSpiAndCfg : 176|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrOutStage : 168|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrPattern : 160|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrShortCircuit : 152|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrShutDown : 144|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrExternal : 136|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrInternal2 : 128|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrInternal1 : 120|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_SpecialEvent : 112|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_CFet_ErrOverview : 104|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrDigOut : 96|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrCurSnsAmp3 : 88|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrCurSnsAmp12 : 80|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrSpiAndCfg : 72|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrOutStage : 64|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrPattern : 56|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrShortCircuit : 48|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrShutDown : 40|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrExternal : 32|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrInternal2 : 24|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ ERR_DFet_ErrInternal1 : 16|8@1+ (1,0) [0|255] ""  RTE,EMB_SSW
 SG_ ERR_DFet_SpecialEvent : 8|8@1+ (1,0) [0|255] ""  RTE,EMB_SSW
 SG_ ERR_DFet_ErrOverview : 0|8@1+ (1,0) [0|255] ""  RTE,EMB_SSW

BO_ 2645185856 TP_DBG_LOG: 64 BMS_MV
 SG_ LOG_31_EventID m31 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_31_Second m31 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_31_Minute m31 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_31_Hour m31 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_31_Day m31 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_31_Month m31 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_31_Year m31 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_31_BasicSOC m31 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_31_TemperatureDMOS02 m31 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_TemperatureDMOS01 m31 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_TemperatureCMOS02 m31 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_TemperatureCMOS01 m31 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_LinkVoltage m31 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_ShuntCurrent m31 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_31_CellTemperature4 m31 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_CellTemperature3 m31 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_CellTemperature2 m31 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_CellTemperature1 m31 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_CellTemperature0 m31 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_31_StackVoltage m31 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage11 m31 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage10 m31 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage9 m31 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage8 m31 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage7 m31 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage6 m31 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage5 m31 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage4 m31 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage3 m31 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage2 m31 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage1 m31 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_CellVoltage0 m31 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_31_RecordCount m31 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_30_EventID m30 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_30_Second m30 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_30_Minute m30 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_30_Hour m30 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_30_Day m30 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_30_Month m30 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_30_Year m30 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_30_BasicSOC m30 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_30_TemperatureDMOS02 m30 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_TemperatureDMOS01 m30 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_TemperatureCMOS02 m30 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_TemperatureCMOS01 m30 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_LinkVoltage m30 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_ShuntCurrent m30 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_30_CellTemperature4 m30 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_CellTemperature3 m30 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_CellTemperature2 m30 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_CellTemperature1 m30 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_CellTemperature0 m30 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_30_StackVoltage m30 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage11 m30 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage10 m30 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage9 m30 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage8 m30 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage7 m30 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage6 m30 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage5 m30 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage4 m30 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage3 m30 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage2 m30 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage1 m30 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_CellVoltage0 m30 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_30_RecordCount m30 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_29_EventID m29 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_29_Second m29 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_29_Minute m29 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_29_Hour m29 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_29_Day m29 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_29_Month m29 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_29_Year m29 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_29_BasicSOC m29 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_29_TemperatureDMOS02 m29 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_TemperatureDMOS01 m29 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_TemperatureCMOS02 m29 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_TemperatureCMOS01 m29 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_LinkVoltage m29 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_ShuntCurrent m29 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_29_CellTemperature4 m29 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_CellTemperature3 m29 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_CellTemperature2 m29 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_CellTemperature1 m29 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_CellTemperature0 m29 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_29_StackVoltage m29 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage11 m29 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage10 m29 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage9 m29 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage8 m29 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage7 m29 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage6 m29 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage5 m29 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage4 m29 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage3 m29 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage2 m29 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage1 m29 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_CellVoltage0 m29 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_29_RecordCount m29 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_28_EventID m28 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_28_Second m28 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_28_Minute m28 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_28_Hour m28 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_28_Day m28 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_28_Month m28 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_28_Year m28 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_28_BasicSOC m28 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_28_TemperatureDMOS02 m28 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_TemperatureDMOS01 m28 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_TemperatureCMOS02 m28 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_TemperatureCMOS01 m28 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_LinkVoltage m28 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_ShuntCurrent m28 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_28_CellTemperature4 m28 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_CellTemperature3 m28 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_CellTemperature2 m28 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_CellTemperature1 m28 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_CellTemperature0 m28 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_28_StackVoltage m28 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage11 m28 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage10 m28 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage9 m28 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage8 m28 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage7 m28 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage6 m28 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage5 m28 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage4 m28 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage3 m28 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage2 m28 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage1 m28 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_CellVoltage0 m28 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_28_RecordCount m28 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_27_EventID m27 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_27_Second m27 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_27_Minute m27 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_27_Hour m27 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_27_Day m27 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_27_Month m27 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_27_Year m27 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_27_BasicSOC m27 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_27_TemperatureDMOS02 m27 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_TemperatureDMOS01 m27 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_TemperatureCMOS02 m27 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_TemperatureCMOS01 m27 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_LinkVoltage m27 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_ShuntCurrent m27 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_27_CellTemperature4 m27 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_CellTemperature3 m27 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_CellTemperature2 m27 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_CellTemperature1 m27 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_CellTemperature0 m27 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_27_StackVoltage m27 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage11 m27 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage10 m27 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage9 m27 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage8 m27 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage7 m27 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage6 m27 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage5 m27 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage4 m27 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage3 m27 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage2 m27 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage1 m27 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_CellVoltage0 m27 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_27_RecordCount m27 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_26_EventID m26 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_26_Second m26 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_26_Minute m26 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_26_Hour m26 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_26_Day m26 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_26_Month m26 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_26_Year m26 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_26_BasicSOC m26 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_26_TemperatureDMOS02 m26 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_TemperatureDMOS01 m26 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_TemperatureCMOS02 m26 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_TemperatureCMOS01 m26 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_LinkVoltage m26 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_ShuntCurrent m26 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_26_CellTemperature4 m26 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_CellTemperature3 m26 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_CellTemperature2 m26 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_CellTemperature1 m26 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_CellTemperature0 m26 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_26_StackVoltage m26 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage11 m26 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage10 m26 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage9 m26 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage8 m26 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage7 m26 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage6 m26 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage5 m26 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage4 m26 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage3 m26 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage2 m26 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage1 m26 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_CellVoltage0 m26 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_26_RecordCount m26 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_25_EventID m25 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_25_Second m25 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_25_Minute m25 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_25_Hour m25 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_25_Day m25 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_25_Month m25 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_25_Year m25 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_25_BasicSOC m25 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_25_TemperatureDMOS02 m25 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_TemperatureDMOS01 m25 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_TemperatureCMOS02 m25 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_TemperatureCMOS01 m25 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_LinkVoltage m25 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_ShuntCurrent m25 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_25_CellTemperature4 m25 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_CellTemperature3 m25 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_CellTemperature2 m25 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_CellTemperature1 m25 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_CellTemperature0 m25 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_25_StackVoltage m25 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage11 m25 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage10 m25 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage9 m25 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage8 m25 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage7 m25 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage6 m25 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage5 m25 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage4 m25 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage3 m25 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage2 m25 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage1 m25 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_CellVoltage0 m25 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_25_RecordCount m25 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_24_EventID m24 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_24_Second m24 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_24_Minute m24 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_24_Hour m24 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_24_Day m24 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_24_Month m24 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_24_Year m24 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_24_BasicSOC m24 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_24_TemperatureDMOS02 m24 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_TemperatureDMOS01 m24 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_TemperatureCMOS02 m24 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_TemperatureCMOS01 m24 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_LinkVoltage m24 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_ShuntCurrent m24 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_24_CellTemperature4 m24 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_CellTemperature3 m24 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_CellTemperature2 m24 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_CellTemperature1 m24 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_CellTemperature0 m24 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_24_StackVoltage m24 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage11 m24 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage10 m24 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage9 m24 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage8 m24 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage7 m24 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage6 m24 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage5 m24 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage4 m24 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage3 m24 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage2 m24 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage1 m24 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_CellVoltage0 m24 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_24_RecordCount m24 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_23_EventID m23 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_23_Second m23 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_23_Minute m23 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_23_Hour m23 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_23_Day m23 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_23_Month m23 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_23_Year m23 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_23_BasicSOC m23 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_23_TemperatureDMOS02 m23 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_TemperatureDMOS01 m23 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_TemperatureCMOS02 m23 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_TemperatureCMOS01 m23 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_LinkVoltage m23 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_ShuntCurrent m23 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_23_CellTemperature4 m23 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_CellTemperature3 m23 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_CellTemperature2 m23 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_CellTemperature1 m23 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_CellTemperature0 m23 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_23_StackVoltage m23 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage11 m23 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage10 m23 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage9 m23 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage8 m23 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage7 m23 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage6 m23 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage5 m23 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage4 m23 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage3 m23 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage2 m23 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage1 m23 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_CellVoltage0 m23 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_23_RecordCount m23 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_22_EventID m22 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_22_Second m22 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_22_Minute m22 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_22_Hour m22 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_22_Day m22 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_22_Month m22 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_22_Year m22 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_22_BasicSOC m22 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_22_TemperatureDMOS02 m22 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_TemperatureDMOS01 m22 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_TemperatureCMOS02 m22 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_TemperatureCMOS01 m22 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_LinkVoltage m22 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_ShuntCurrent m22 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_22_CellTemperature4 m22 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_CellTemperature3 m22 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_CellTemperature2 m22 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_CellTemperature1 m22 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_CellTemperature0 m22 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_22_StackVoltage m22 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage11 m22 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage10 m22 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage9 m22 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage8 m22 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage7 m22 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage6 m22 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage5 m22 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage4 m22 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage3 m22 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage2 m22 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage1 m22 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_CellVoltage0 m22 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_22_RecordCount m22 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_21_EventID m21 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_21_Second m21 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_21_Minute m21 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_21_Hour m21 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_21_Day m21 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_21_Month m21 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_21_Year m21 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_21_BasicSOC m21 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_21_TemperatureDMOS02 m21 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_TemperatureDMOS01 m21 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_TemperatureCMOS02 m21 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_TemperatureCMOS01 m21 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_LinkVoltage m21 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_ShuntCurrent m21 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_21_CellTemperature4 m21 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_CellTemperature3 m21 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_CellTemperature2 m21 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_CellTemperature1 m21 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_CellTemperature0 m21 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_21_StackVoltage m21 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage11 m21 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage10 m21 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage9 m21 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage8 m21 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage7 m21 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage6 m21 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage5 m21 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage4 m21 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage3 m21 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage2 m21 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage1 m21 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_CellVoltage0 m21 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_21_RecordCount m21 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_20_EventID m20 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_20_Second m20 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_20_Minute m20 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_20_Hour m20 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_20_Day m20 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_20_Month m20 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_20_Year m20 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_20_BasicSOC m20 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_20_TemperatureDMOS02 m20 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_TemperatureDMOS01 m20 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_TemperatureCMOS02 m20 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_TemperatureCMOS01 m20 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_LinkVoltage m20 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_ShuntCurrent m20 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_20_CellTemperature4 m20 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_CellTemperature3 m20 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_CellTemperature2 m20 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_CellTemperature1 m20 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_CellTemperature0 m20 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_20_StackVoltage m20 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage11 m20 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage10 m20 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage9 m20 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage8 m20 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage7 m20 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage6 m20 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage5 m20 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage4 m20 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage3 m20 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage2 m20 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage1 m20 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_CellVoltage0 m20 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_20_RecordCount m20 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_19_EventID m19 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_19_Second m19 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_19_Minute m19 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_19_Hour m19 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_19_Day m19 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_19_Month m19 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_19_Year m19 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_19_BasicSOC m19 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_19_TemperatureDMOS02 m19 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_TemperatureDMOS01 m19 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_TemperatureCMOS02 m19 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_TemperatureCMOS01 m19 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_LinkVoltage m19 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_ShuntCurrent m19 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_19_CellTemperature4 m19 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_CellTemperature3 m19 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_CellTemperature2 m19 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_CellTemperature1 m19 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_CellTemperature0 m19 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_19_StackVoltage m19 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage11 m19 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage10 m19 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage9 m19 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage8 m19 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage7 m19 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage6 m19 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage5 m19 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage4 m19 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage3 m19 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage2 m19 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage1 m19 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_CellVoltage0 m19 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_19_RecordCount m19 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_18_EventID m18 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_18_Second m18 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_18_Minute m18 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_18_Hour m18 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_18_Day m18 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_18_Month m18 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_18_Year m18 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_18_BasicSOC m18 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_18_TemperatureDMOS02 m18 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_TemperatureDMOS01 m18 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_TemperatureCMOS02 m18 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_TemperatureCMOS01 m18 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_LinkVoltage m18 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_ShuntCurrent m18 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_18_CellTemperature4 m18 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_CellTemperature3 m18 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_CellTemperature2 m18 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_CellTemperature1 m18 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_CellTemperature0 m18 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_18_StackVoltage m18 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage11 m18 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage10 m18 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage9 m18 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage8 m18 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage7 m18 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage6 m18 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage5 m18 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage4 m18 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage3 m18 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage2 m18 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage1 m18 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_CellVoltage0 m18 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_18_RecordCount m18 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_17_EventID m17 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_17_Second m17 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_17_Minute m17 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_17_Hour m17 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_17_Day m17 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_17_Month m17 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_17_Year m17 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_17_BasicSOC m17 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_17_TemperatureDMOS02 m17 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_TemperatureDMOS01 m17 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_TemperatureCMOS02 m17 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_TemperatureCMOS01 m17 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_LinkVoltage m17 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_ShuntCurrent m17 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_17_CellTemperature4 m17 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_CellTemperature3 m17 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_CellTemperature2 m17 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_CellTemperature1 m17 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_CellTemperature0 m17 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_17_StackVoltage m17 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage11 m17 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage10 m17 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage9 m17 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage8 m17 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage7 m17 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage6 m17 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage5 m17 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage4 m17 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage3 m17 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage2 m17 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage1 m17 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_CellVoltage0 m17 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_17_RecordCount m17 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_16_EventID m16 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_16_Second m16 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_16_Minute m16 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_16_Hour m16 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_16_Day m16 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_16_Month m16 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_16_Year m16 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_16_BasicSOC m16 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_16_TemperatureDMOS02 m16 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_TemperatureDMOS01 m16 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_TemperatureCMOS02 m16 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_TemperatureCMOS01 m16 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_LinkVoltage m16 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_ShuntCurrent m16 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_16_CellTemperature4 m16 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_CellTemperature3 m16 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_CellTemperature2 m16 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_CellTemperature1 m16 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_CellTemperature0 m16 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_16_StackVoltage m16 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage11 m16 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage10 m16 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage9 m16 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage8 m16 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage7 m16 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage6 m16 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage5 m16 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage4 m16 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage3 m16 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage2 m16 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage1 m16 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_CellVoltage0 m16 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_16_RecordCount m16 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_15_EventID m15 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_15_Second m15 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_15_Minute m15 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_15_Hour m15 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_15_Day m15 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_15_Month m15 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_15_Year m15 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_15_BasicSOC m15 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_15_TemperatureDMOS02 m15 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_TemperatureDMOS01 m15 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_TemperatureCMOS02 m15 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_TemperatureCMOS01 m15 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_LinkVoltage m15 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_ShuntCurrent m15 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_15_CellTemperature4 m15 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_CellTemperature3 m15 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_CellTemperature2 m15 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_CellTemperature1 m15 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_CellTemperature0 m15 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_15_StackVoltage m15 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage11 m15 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage10 m15 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage9 m15 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage8 m15 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage7 m15 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage6 m15 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage5 m15 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage4 m15 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage3 m15 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage2 m15 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage1 m15 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_CellVoltage0 m15 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_15_RecordCount m15 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_14_EventID m14 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_14_Second m14 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_14_Minute m14 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_14_Hour m14 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_14_Day m14 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_14_Month m14 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_14_Year m14 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_14_BasicSOC m14 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_14_TemperatureDMOS02 m14 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_TemperatureDMOS01 m14 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_TemperatureCMOS02 m14 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_TemperatureCMOS01 m14 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_LinkVoltage m14 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_ShuntCurrent m14 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_14_CellTemperature4 m14 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_CellTemperature3 m14 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_CellTemperature2 m14 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_CellTemperature1 m14 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_CellTemperature0 m14 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_14_StackVoltage m14 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage11 m14 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage10 m14 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage9 m14 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage8 m14 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage7 m14 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage6 m14 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage5 m14 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage4 m14 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage3 m14 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage2 m14 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage1 m14 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_CellVoltage0 m14 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_14_RecordCount m14 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_13_EventID m13 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_13_Second m13 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_13_Minute m13 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_13_Hour m13 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_13_Day m13 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_13_Month m13 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_13_Year m13 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_13_BasicSOC m13 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_13_TemperatureDMOS02 m13 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_TemperatureDMOS01 m13 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_TemperatureCMOS02 m13 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_TemperatureCMOS01 m13 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_LinkVoltage m13 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_ShuntCurrent m13 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_13_CellTemperature4 m13 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_CellTemperature3 m13 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_CellTemperature2 m13 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_CellTemperature1 m13 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_CellTemperature0 m13 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_13_StackVoltage m13 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage11 m13 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage10 m13 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage9 m13 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage8 m13 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage7 m13 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage6 m13 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage5 m13 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage4 m13 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage3 m13 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage2 m13 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage1 m13 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_CellVoltage0 m13 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_13_RecordCount m13 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_12_EventID m12 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_12_Second m12 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_12_Minute m12 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_12_Hour m12 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_12_Day m12 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_12_Month m12 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_12_Year m12 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_12_BasicSOC m12 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_12_TemperatureDMOS02 m12 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_TemperatureDMOS01 m12 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_TemperatureCMOS02 m12 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_TemperatureCMOS01 m12 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_LinkVoltage m12 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_ShuntCurrent m12 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_12_CellTemperature4 m12 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_CellTemperature3 m12 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_CellTemperature2 m12 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_CellTemperature1 m12 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_CellTemperature0 m12 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_12_StackVoltage m12 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage11 m12 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage10 m12 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage9 m12 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage8 m12 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage7 m12 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage6 m12 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage5 m12 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage4 m12 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage3 m12 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage2 m12 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage1 m12 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_CellVoltage0 m12 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_12_RecordCount m12 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_11_EventID m11 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_11_Second m11 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_11_Minute m11 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_11_Hour m11 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_11_Day m11 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_11_Month m11 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_11_Year m11 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_11_BasicSOC m11 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_11_TemperatureDMOS02 m11 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_TemperatureDMOS01 m11 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_TemperatureCMOS02 m11 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_TemperatureCMOS01 m11 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_LinkVoltage m11 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_ShuntCurrent m11 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_11_CellTemperature4 m11 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_CellTemperature3 m11 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_CellTemperature2 m11 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_CellTemperature1 m11 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_CellTemperature0 m11 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_11_StackVoltage m11 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage11 m11 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage10 m11 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage9 m11 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage8 m11 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage7 m11 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage6 m11 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage5 m11 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage4 m11 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage3 m11 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage2 m11 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage1 m11 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_CellVoltage0 m11 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_11_RecordCount m11 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_10_EventID m10 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_10_Second m10 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_10_Minute m10 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_10_Hour m10 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_10_Day m10 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_10_Month m10 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_10_Year m10 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_10_BasicSOC m10 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_10_TemperatureDMOS02 m10 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_TemperatureDMOS01 m10 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_TemperatureCMOS02 m10 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_TemperatureCMOS01 m10 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_LinkVoltage m10 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_ShuntCurrent m10 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_10_CellTemperature4 m10 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_CellTemperature3 m10 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_CellTemperature2 m10 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_CellTemperature1 m10 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_CellTemperature0 m10 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_10_StackVoltage m10 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage11 m10 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage10 m10 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage9 m10 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage8 m10 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage7 m10 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage6 m10 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage5 m10 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage4 m10 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage3 m10 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage2 m10 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage1 m10 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_CellVoltage0 m10 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_10_RecordCount m10 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_9_EventID m9 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_9_Second m9 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_9_Minute m9 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_9_Hour m9 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_9_Day m9 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_9_Month m9 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_9_Year m9 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_9_BasicSOC m9 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_9_TemperatureDMOS02 m9 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_TemperatureDMOS01 m9 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_TemperatureCMOS02 m9 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_TemperatureCMOS01 m9 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_LinkVoltage m9 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_ShuntCurrent m9 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_9_CellTemperature4 m9 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_CellTemperature3 m9 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_CellTemperature2 m9 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_CellTemperature1 m9 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_CellTemperature0 m9 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_9_StackVoltage m9 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage11 m9 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage10 m9 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage9 m9 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage8 m9 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage7 m9 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage6 m9 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage5 m9 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage4 m9 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage3 m9 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage2 m9 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage1 m9 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_CellVoltage0 m9 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_9_RecordCount m9 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_8_EventID m8 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_8_Second m8 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_8_Minute m8 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_8_Hour m8 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_8_Day m8 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_8_Month m8 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_8_Year m8 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_8_BasicSOC m8 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_8_TemperatureDMOS02 m8 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_TemperatureDMOS01 m8 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_TemperatureCMOS02 m8 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_TemperatureCMOS01 m8 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_LinkVoltage m8 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_ShuntCurrent m8 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_8_CellTemperature4 m8 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_CellTemperature3 m8 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_CellTemperature2 m8 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_CellTemperature1 m8 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_CellTemperature0 m8 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_8_StackVoltage m8 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage11 m8 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage10 m8 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage9 m8 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage8 m8 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage7 m8 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage6 m8 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage5 m8 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage4 m8 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage3 m8 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage2 m8 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage1 m8 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_CellVoltage0 m8 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_8_RecordCount m8 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_7_EventID m7 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_7_Second m7 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_7_Minute m7 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_7_Hour m7 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_7_Day m7 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_7_Month m7 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_7_Year m7 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_7_BasicSOC m7 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_7_TemperatureDMOS02 m7 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_TemperatureDMOS01 m7 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_TemperatureCMOS02 m7 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_TemperatureCMOS01 m7 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_LinkVoltage m7 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_ShuntCurrent m7 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_7_CellTemperature4 m7 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_CellTemperature3 m7 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_CellTemperature2 m7 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_CellTemperature1 m7 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_CellTemperature0 m7 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_7_StackVoltage m7 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage11 m7 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage10 m7 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage9 m7 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage8 m7 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage7 m7 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage6 m7 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage5 m7 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage4 m7 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage3 m7 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage2 m7 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage1 m7 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_CellVoltage0 m7 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_7_RecordCount m7 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_6_EventID m6 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_6_Second m6 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_6_Minute m6 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_6_Hour m6 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_6_Day m6 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_6_Month m6 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_6_Year m6 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_6_BasicSOC m6 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_6_TemperatureDMOS02 m6 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_TemperatureDMOS01 m6 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_TemperatureCMOS02 m6 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_TemperatureCMOS01 m6 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_LinkVoltage m6 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_ShuntCurrent m6 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_6_CellTemperature4 m6 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_CellTemperature3 m6 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_CellTemperature2 m6 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_CellTemperature1 m6 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_CellTemperature0 m6 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_6_StackVoltage m6 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage11 m6 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage10 m6 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage9 m6 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage8 m6 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage7 m6 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage6 m6 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage5 m6 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage4 m6 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage3 m6 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage2 m6 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage1 m6 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_CellVoltage0 m6 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_6_RecordCount m6 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_5_EventID m5 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_5_Second m5 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_5_Minute m5 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_5_Hour m5 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_5_Day m5 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_5_Month m5 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_5_Year m5 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_5_BasicSOC m5 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_5_TemperatureDMOS02 m5 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_TemperatureDMOS01 m5 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_TemperatureCMOS02 m5 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_TemperatureCMOS01 m5 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_LinkVoltage m5 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_ShuntCurrent m5 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_5_CellTemperature4 m5 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_CellTemperature3 m5 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_CellTemperature2 m5 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_CellTemperature1 m5 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_CellTemperature0 m5 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_5_StackVoltage m5 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage11 m5 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage10 m5 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage9 m5 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage8 m5 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage7 m5 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage6 m5 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage5 m5 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage4 m5 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage3 m5 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage2 m5 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage1 m5 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_CellVoltage0 m5 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_5_RecordCount m5 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_4_EventID m4 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_4_Second m4 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_4_Minute m4 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_4_Hour m4 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_4_Day m4 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_4_Month m4 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_4_Year m4 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_4_BasicSOC m4 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_4_TemperatureDMOS02 m4 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_TemperatureDMOS01 m4 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_TemperatureCMOS02 m4 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_TemperatureCMOS01 m4 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_LinkVoltage m4 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_ShuntCurrent m4 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_4_CellTemperature4 m4 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_CellTemperature3 m4 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_CellTemperature2 m4 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_CellTemperature1 m4 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_CellTemperature0 m4 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_4_StackVoltage m4 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage11 m4 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage10 m4 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage9 m4 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage8 m4 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage7 m4 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage6 m4 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage5 m4 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage4 m4 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage3 m4 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage2 m4 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage1 m4 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_CellVoltage0 m4 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_4_RecordCount m4 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_3_EventID m3 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_3_Second m3 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_3_Minute m3 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_3_Hour m3 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_3_Day m3 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_3_Month m3 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_3_Year m3 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_3_BasicSOC m3 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_3_TemperatureDMOS02 m3 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_TemperatureDMOS01 m3 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_TemperatureCMOS02 m3 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_TemperatureCMOS01 m3 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_LinkVoltage m3 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_ShuntCurrent m3 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_3_CellTemperature4 m3 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_CellTemperature3 m3 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_CellTemperature2 m3 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_CellTemperature1 m3 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_CellTemperature0 m3 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_3_StackVoltage m3 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage11 m3 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage10 m3 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage9 m3 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage8 m3 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage7 m3 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage6 m3 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage5 m3 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage4 m3 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage3 m3 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage2 m3 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage1 m3 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_CellVoltage0 m3 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_3_RecordCount m3 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_2_EventID m2 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_2_Second m2 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_2_Minute m2 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_2_Hour m2 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_2_Day m2 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_2_Month m2 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_2_Year m2 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_2_BasicSOC m2 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_2_TemperatureDMOS02 m2 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_TemperatureDMOS01 m2 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_TemperatureCMOS02 m2 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_TemperatureCMOS01 m2 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_LinkVoltage m2 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_ShuntCurrent m2 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_2_CellTemperature4 m2 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_CellTemperature3 m2 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_CellTemperature2 m2 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_CellTemperature1 m2 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_CellTemperature0 m2 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_2_StackVoltage m2 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage11 m2 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage10 m2 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage9 m2 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage8 m2 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage7 m2 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage6 m2 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage5 m2 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage4 m2 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage3 m2 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage2 m2 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage1 m2 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_CellVoltage0 m2 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_2_RecordCount m2 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_1_EventID m1 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_1_Second m1 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_1_Minute m1 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_1_Hour m1 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_1_Day m1 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_1_Month m1 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_1_Year m1 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_1_BasicSOC m1 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_1_TemperatureDMOS02 m1 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_TemperatureDMOS01 m1 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_TemperatureCMOS02 m1 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_TemperatureCMOS01 m1 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_LinkVoltage m1 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_ShuntCurrent m1 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_1_CellTemperature4 m1 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_CellTemperature3 m1 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_CellTemperature2 m1 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_CellTemperature1 m1 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_CellTemperature0 m1 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_1_StackVoltage m1 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage11 m1 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage10 m1 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage9 m1 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage8 m1 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage7 m1 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage6 m1 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage5 m1 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage4 m1 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage3 m1 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage2 m1 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage1 m1 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_CellVoltage0 m1 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_1_RecordCount m1 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_0_EventID m0 : 504|8@1+ (1,0) [0|63] ""  EMB_SSW,RTE
 SG_ LOG_0_Second m0 : 496|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_0_Minute m0 : 488|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ LOG_0_Hour m0 : 480|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ LOG_0_Day m0 : 472|8@1+ (1,0) [0|31] ""  EMB_SSW,RTE
 SG_ LOG_0_Month m0 : 464|8@1+ (1,0) [0|12] ""  EMB_SSW,RTE
 SG_ LOG_0_Year m0 : 448|16@1+ (1,0) [0|99] ""  EMB_SSW,RTE
 SG_ LOG_0_BasicSOC m0 : 432|16@1+ (0.01,0) [0|100] ""  EMB_SSW,RTE
 SG_ LOG_0_TemperatureDMOS02 m0 : 416|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_TemperatureDMOS01 m0 : 400|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_TemperatureCMOS02 m0 : 384|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_TemperatureCMOS01 m0 : 368|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_LinkVoltage m0 : 352|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_ShuntCurrent m0 : 320|32@1- (1,0) [-2147483648|2147483646] ""  EMB_SSW,RTE
 SG_ LOG_0_CellTemperature4 m0 : 304|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_CellTemperature3 m0 : 288|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_CellTemperature2 m0 : 272|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_CellTemperature1 m0 : 256|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_CellTemperature0 m0 : 240|16@1- (0.1,0) [-3276.8|3276.7] ""  EMB_SSW,RTE
 SG_ LOG_0_StackVoltage m0 : 224|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage11 m0 : 208|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage10 m0 : 192|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage9 m0 : 176|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage8 m0 : 160|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage7 m0 : 144|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage6 m0 : 128|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage5 m0 : 112|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage4 m0 : 96|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage3 m0 : 80|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage2 m0 : 64|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage1 m0 : 48|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_CellVoltage0 m0 : 32|16@1+ (0.001,0) [0|65.534] ""  EMB_SSW,RTE
 SG_ LOG_0_RecordCount m0 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ LOG_RecordIndex M : 0|16@1+ (1,0) [0|65535] ""  RTE,EMB_SSW

BO_ 2147483773 EMB_DBG_HW_REVISION: 1 BMS_MV
 SG_ hw_version : 0|8@1+ (1,0) [0|256] ""  EMB_SSW,RTE

BO_ 2147487752 VW_DBG_Meas4: 64 BMS_MV
 SG_ s_IMaxDischarge : 384|18@1+ (0.01,0) [0|2621.43] "A"  EMB_SSW,RTE
 SG_ s_IMaxCharge : 358|18@1+ (0.01,0) [0|2621.43] "A"  EMB_SSW,RTE
 SG_ s_UMinDischarge : 347|11@1+ (0.03125,0) [0|63.96875] "Volt"  EMB_SSW,RTE
 SG_ s_UMaxCharge : 336|11@1+ (0.03125,0) [0|63.96875] "Volt"  EMB_SSW,RTE
 SG_ qQBatTotal_mu8 : 312|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ QBatTotal_mu16 : 320|16@1+ (0.1,0) [0|6553.5] "Ah"  EMB_SSW,RTE
 SG_ cpa_bOCVReady_mb : 304|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ przSOCOCV_mu16 : 288|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellMin : 272|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellMax : 256|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ qprzBatSOC_mu8 : 308|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ qprzSysSOCEP_mu8 : 305|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_przSOCCellArray_9 : 208|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_8 : 192|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_7 : 176|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_6 : 160|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_5 : 144|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_4 : 128|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_3 : 112|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_2 : 96|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_11 : 240|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_10 : 224|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_1 : 80|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCCellArray_0 : 64|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ przBatSOC_mu16 : 48|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ SOCBmsBasicDyn : 0|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ SOCBmsBasic : 16|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ s_przSOCBMS : 32|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE

BO_ 2147487746 VW_DBG_Meas2: 64 BMS_MV
 SG_ NMH_State : 96|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ UBatStack : 80|16@1+ (0.001,0) [0|65.535] "V"  EMB_SSW,RTE
 SG_ EOCBState : 296|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_bShtDwnPrevHwResetCycle : 76|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_bal_bCalcCompl : 222|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_tBalWakeUp_min : 304|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_11 : 496|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_10 : 480|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_9 : 464|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_8 : 448|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_7 : 432|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_6 : 416|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_5 : 400|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_4 : 384|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_3 : 368|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_2 : 352|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_1 : 336|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_tBalTimeArray_min_0 : 320|16@1+ (1,0) [0|65535] "min"  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_9 : 280|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_8 : 284|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_7 : 272|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_6 : 276|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_5 : 264|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_4 : 268|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_3 : 256|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_2 : 260|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_11 : 292|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_10 : 288|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_1 : 252|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusQualifierArray_0 : 248|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_11 : 246|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_10 : 244|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_9 : 242|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_8 : 240|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_7 : 238|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_6 : 236|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_5 : 234|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_4 : 232|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_3 : 230|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_2 : 228|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_1 : 226|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ s_blBalStatusArray_0 : 224|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ OMM_State : 12|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ AliveCounter : 72|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ TimeSinceStart : 40|32@1+ (1,0) [0|4294967295] "ms"  EMB_SSW,RTE
 SG_ KL75 : 21|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ KL15 : 20|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ s_blInitBMS : 16|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ vVehicle : 24|16@1+ (0.01,0) [0|655.35] "km/h"  EMB_SSW,RTE
 SG_ UCellLpAvg : 208|14@1- (0.001,0) [-8.192|8.191] "V"  EMB_SSW,RTE
 SG_ UCellMax : 176|14@1- (0.001,0) [-8.192|8.191] "V"  RTE,EMB_SSW
 SG_ UCellDelta : 192|14@1- (0.001,0) [-8.192|8.191] "V"  EMB_SSW,RTE
 SG_ UCellMin : 160|14@1- (0.001,0) [-8.192|8.191] "V"  EMB_SSW,RTE
 SG_ s_blExtRangeReq : 22|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ SOCColdCrank : 144|14@1+ (0.01,0) [0|100] "%"  EMB_SSW,RTE
 SG_ enModeReq_mu8 : 17|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valActualModeInt : 8|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ s_valTargetModeBMS : 0|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valActualModeBMS : 3|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE

BO_ 2147487750 VW_DBG_Meas3: 64 BMS_MV
 SG_ s_PminColdCranking : 452|26@1- (1E-006,0) [-33.554432|33.554431] "kW"  EMB_SSW,RTE
 SG_ s_PCool : 478|26@1- (0.001,0) [-33554.432|33554.431] "W"  EMB_SSW,RTE
 SG_ s_PColdCrankingSupport : 426|26@1- (1E-006,0) [-33.554432|33.554431] "kW"  EMB_SSW,RTE
 SG_ s_PBatColdCrank : 400|26@1- (1E-006,0) [-33.554432|33.554431] "kW"  EMB_SSW,RTE
 SG_ SpeedFan : 360|14@1+ (1,0) [0|16383] "rpm"  EMB_SSW,RTE
 SG_ PwmFan2 : 352|7@1+ (1,0) [0|100] "%"  EMB_SSW,RTE
 SG_ PwmFan1 : 344|7@1+ (1,0) [0|100] "%"  EMB_SSW,RTE
 SG_ FanEnableRequest : 343|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ HeatingEnabled : 359|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ CoolingEnabled : 351|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ TCellMin : 328|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ TCellMax : 316|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ TBat : 304|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_tSleepTime : 376|23@1+ (1,0) [0|8388607] "s"  EMB_SSW,RTE
 SG_ s_valErrQAHCount : 237|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_QDischarge : 272|32@1+ (0.001,0) [0|4294967.295] "As"  EMB_SSW,RTE
 SG_ s_QCharge : 240|32@1+ (0.001,0) [0|4294967.295] "As"  EMB_SSW,RTE
 SG_ s_TOutlet_Celsius : 222|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TInlet_Celsius : 207|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_valErrTCellArray_4 : 156|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTCellArray_3 : 153|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTCellArray_2 : 150|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTCellArray_1 : 147|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTCellArray_0 : 144|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTOutlet : 234|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrTInlet : 219|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_TBat_Celsius : 195|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TDeltaCell : 183|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellMin_Celsius : 171|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellMax_Celsius : 159|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellArray_Celsius_4 : 132|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellArray_Celsius_3 : 120|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellArray_Celsius_2 : 108|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellArray_Celsius_1 : 96|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TCellArray_Celsius_0 : 84|12@1- (0.1,0) [-204.8|204.7] "�C"  EMB_SSW,RTE
 SG_ s_TOutletRaw_Celsius : 72|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TInletRaw_Celsius : 60|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TCellArrayRaw_Celsius_4 : 48|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TCellArrayRaw_Celsius_3 : 36|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TCellArrayRaw_Celsius_2 : 24|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TCellArrayRaw_Celsius_1 : 12|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW
 SG_ s_TCellArrayRaw_Celsius_0 : 0|12@1- (0.1,0) [-204.8|204.7] "�C"  RTE,EMB_SSW

BO_ 2147487748 VW_DBG_Faults: 64 BMS_MV
 SG_ F_FUSE_FAILURE : 98|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ ser_qbEbatOK_mu8 : 428|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ ser_bEbatOK_mb : 427|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ DBGCAN_TxQueueFullCounter_XL : 360|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_TxQueueFullCounter_L : 352|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_TxQueueFullCounter_M : 344|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_TxQueueFullCounter_S : 336|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_RxQueueFullCounter_XL : 328|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_RxQueueFullCounter_L : 320|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_RxQueueFullCounter_M : 312|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ DBGCAN_RxQueueFullCounter_S : 304|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_TxQueueFullCounter_XL : 296|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_TxQueueFullCounter_S : 272|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_TxQueueFullCounter_M : 280|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_TxQueueFullCounter_L : 288|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_RxQueueFullCounter_XL : 264|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_RxQueueFullCounter_L : 256|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_RxQueueFullCounter_M : 248|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ VCAN_RxQueueFullCounter_S : 240|8@1+ (1,0) [0|100] "Overflow"  EMB_SSW,RTE
 SG_ TimeSinceLastTargetModeMsg : 192|16@1+ (1,0) [0|65535] "ms"  EMB_SSW,RTE
 SG_ TargetModeMessageReceived : 208|2@1+ (1,0) [0|3] ""  EMB_SSW,RTE
 SG_ F_PRECHARGE_TO : 96|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ s_blEmergencyOperationModeReq : 415|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ s_valCrashShutdownSWReq : 411|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valEResEmergency : 424|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valDeepDischarge : 408|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_cntDeepDischarge : 416|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ s_blErrBatteryOffReq : 406|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ s_blErrBatteryOff : 407|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ s_valErrStatusBMS : 403|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ s_valErrAirflow : 400|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ F_FAN_ROTOR_LOCKED : 94|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_AIRDUCT_BLOCKED : 92|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_DOC_L3 : 90|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_DOC_L2 : 88|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_DOC_L1 : 86|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_COC_L3 : 84|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_COC_L2 : 82|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_COC_L1 : 80|2@1+ (1,0) [0|0] ""  EMB_SSW,RTE
 SG_ F_SOC_DD_L3 : 78|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_SOC_DD_L2 : 76|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_SOC_DD_L1 : 74|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_MVK_01_TO : 72|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CRASH_CAN_IMPL : 70|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CRASH_CAN_ERRVAL : 68|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CRASH_L2 : 66|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CRASH_L1 : 64|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_GDR_FAILURE : 62|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_AFE_FAILURE : 60|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_MOSFET_FAILURE : 58|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_MOSFET_OT_L3 : 56|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_MOSFET_OT_L2 : 54|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_MOSFET_OT_L1 : 52|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_SCP : 50|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DUT_L4 : 48|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DUT_L3 : 46|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DUT_L2 : 44|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DUT_L1 : 42|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DOT_L4 : 40|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DOT_L3 : 38|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DOT_L2 : 36|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_DOT_L1 : 34|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CUT_L4 : 32|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CUT_L3 : 30|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CUT_L2 : 28|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CUT_L1 : 26|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_COT_L4 : 24|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_COT_L3 : 22|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_COT_L2 : 20|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_COT_L1 : 18|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_CELL_UNBALANCE_L4 : 16|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_UVP_L4 : 14|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_UVP_L3 : 12|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_UVP_L2 : 10|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_UVP_L1 : 8|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_OVP_L4 : 6|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_OVP_L3 : 4|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_OVP_L2 : 2|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW
 SG_ F_OVP_L1 : 0|2@1+ (1,0) [0|0] ""  RTE,EMB_SSW

BO_ 2147487744 VW_DBG_Meas1: 64 BMS_MV
 SG_ s_valErrUCellMinMax : 505|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_UCellMin : 492|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellMax : 479|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_valErrUCellArray_11 : 476|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_10 : 473|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_9 : 470|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_8 : 467|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_7 : 464|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_6 : 461|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_5 : 458|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_4 : 455|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_3 : 452|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_2 : 449|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_1 : 446|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_valErrUCellArray_0 : 443|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_UCellArray_11 : 430|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_10 : 417|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_9 : 404|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_8 : 391|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_7 : 378|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_6 : 365|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_5 : 352|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_4 : 339|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_3 : 326|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_2 : 313|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_1 : 300|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArray_0 : 287|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_PBat_mi32 : 271|16@1- (0.001,0) [-32.768|32.767] "kW"  RTE,EMB_SSW
 SG_ s_valErrUKl40 : 268|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_UKl40 : 252|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ s_valErrUBat : 249|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_Ubat : 233|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ s_valErrIBat : 230|3@1+ (1,0) [0|7] ""  RTE,EMB_SSW
 SG_ s_IBat : 209|21@1- (0.001,0) [-1048.576|1048.575] "A"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_11 : 196|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_10 : 183|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_9 : 170|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_8 : 157|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_7 : 144|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_6 : 131|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_5 : 118|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_4 : 105|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_3 : 92|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_2 : 79|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_1 : 66|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UCellArrayRaw_0 : 53|13@1+ (0.001,0) [0|8.191] "V"  RTE,EMB_SSW
 SG_ s_UKl40Raw : 37|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ s_UbatRaw : 21|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ s_IBatRaw : 0|21@1- (0.001,0) [-1048.576|1048.575] "A"  RTE,EMB_SSW

BO_ 2147484174 EMB_SIM_Voltages: 8 EMB_SSW
 SG_ SIM_Voltage_Stack : 48|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Link : 32|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_12VADC2 : 16|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_12VADC1 : 0|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE

BO_ 2147484172 EMB_SIM_FanTemperatures: 8 EMB_SSW
 SG_ SIM_Temperature_FanOut : 16|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_FanIn : 0|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE

BO_ 2147484170 EMB_SIM_MosfetTemperatures: 8 EMB_SSW
 SG_ SIM_Temperature_DMOS02 : 48|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_DMOS01 : 32|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_CMOS02 : 16|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_CMOS01 : 0|16@1- (0.1,0) [-3276.8|3276.7] "�C"  BMS_MV,RTE

BO_ 2147484169 EMB_SIM_Current: 8 EMB_SSW
 SG_ SIM_Current_Shunt : 32|32@1- (1,0) [-2147483648|2147483646] "mA"  BMS_MV,RTE
 SG_ SIM_Current_HallSensor : 0|32@1- (1,0) [-2147483648|2147483646] "mA"  BMS_MV,RTE

BO_ 2147484167 EMB_SIM_CellTemperatures: 8 EMB_SSW
 SG_ SIM_Temperature_Cell4 : 32|8@1- (1,0) [-128|127] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_Cell3 : 24|8@1- (1,0) [-128|127] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_Cell2 : 16|8@1- (1,0) [-128|127] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_Cell1 : 8|8@1- (1,0) [-128|127] "�C"  BMS_MV,RTE
 SG_ SIM_Temperature_Cell0 : 0|8@1- (1,0) [-128|127] "�C"  BMS_MV,RTE

BO_ 2147484165 EMB_SIM_CellVoltages2: 8 EMB_SSW
 SG_ SIM_Voltage_Cell11 : 48|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell10 : 32|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell9 : 16|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell8 : 0|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE

BO_ 2147484163 EMB_SIM_CellVoltages1: 8 EMB_SSW
 SG_ SIM_Voltage_Cell7 : 48|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell6 : 32|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell5 : 16|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell4 : 0|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE

BO_ 2147484161 EMB_SIM_CellVoltages0: 8 EMB_SSW
 SG_ SIM_Voltage_Cell3 : 48|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell2 : 32|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell1 : 16|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE
 SG_ SIM_Voltage_Cell0 : 0|16@1+ (1,0) [0|65535] "mV"  BMS_MV,RTE

BO_ 2147483790 BMS_CAL_OffsetTempVolt_2: 8 BMS_MV
 SG_ OffsetTempVolt_Tcell05 : 0|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE
 SG_ OffsetTempVolt_T_FAN_Outlet : 32|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE
 SG_ OffsetTempVolt_T_FAN_Inlet : 16|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE

BO_ 2147483788 BMS_CAL_OffsetTempVolt_1: 8 BMS_MV
 SG_ OffsetTempVolt_Tcell04 : 48|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE
 SG_ OffsetTempVolt_Tcell03 : 32|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE
 SG_ OffsetTempVolt_Tcell02 : 16|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE
 SG_ OffsetTempVolt_Tcell01 : 0|16@1- (1,0) [-32768|32767] "mV"  EMB_SSW,RTE

BO_ 2147483786 BMS_CAL_PackVoltageADC: 8 BMS_MV
 SG_ CaliPackVolADC_Low : 16|16@1+ (1,0) [0|65535] "digits"  EMB_SSW,RTE
 SG_ CaliPackVolADC_High : 0|16@1+ (1,0) [0|65535] "digits"  EMB_SSW,RTE

BO_ 2147483784 BMS_CAL_CurrentADC: 8 BMS_MV
 SG_ CaliCurrentADC_Low : 32|32@1- (1,0) [-2147483648|2147483647] "mA"  EMB_SSW,RTE
 SG_ CaliCurrentADC_High : 0|32@1- (1,0) [-2147483648|2147483647] "mA"  EMB_SSW,RTE

BO_ 2147483782 BMS_CAL_Status: 8 BMS_MV
 SG_ VoltageCal_state : 24|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ TemperatureCal_state : 32|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ CurrentCal_state : 16|4@1+ (1,0) [0|15] ""  EMB_SSW,RTE
 SG_ Calibration_status : 0|3@1+ (1,0) [0|7] ""  EMB_SSW,RTE

BO_ 2147483763 TP_CAL_RefTempVoltage2: 8 EMB_SSW
 SG_ RefTempVolt_Tcell05 : 0|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE
 SG_ RefTempVolt_T_FAN_Outlet : 32|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE
 SG_ RefTempVolt_T_FAN_Inlet : 16|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE

BO_ 2147483761 TP_CAL_RefTempVoltage1: 8 EMB_SSW
 SG_ RefTempVolt_Tcell04 : 48|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE
 SG_ RefTempVolt_Tcell03 : 32|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE
 SG_ RefTempVolt_Tcell02 : 16|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE
 SG_ RefTempVolt_Tcell01 : 0|16@1- (1,0) [-32768|32766] "mV"  BMS_MV,RTE

BO_ 2147483759 TP_CAL_RefPackVoltage: 8 EMB_SSW
 SG_ RefPackVolt_Low : 16|16@1+ (1,0) [0|65534] "mV"  BMS_MV,RTE
 SG_ RefPackVolt_High : 0|16@1+ (1,0) [0|65534] "mV"  BMS_MV,RTE

BO_ 2147483757 TP_CAL_RefCurrent: 8 EMB_SSW
 SG_ RefCurrent_Low : 32|32@1- (1,0) [-2147483648|2147483646] "mA"  BMS_MV,RTE
 SG_ RefCurrent_High : 0|32@1- (1,0) [-2147483648|2147483646] "mA"  BMS_MV,RTE

BO_ 2147483755 TP_CAL_Control: 8 EMB_SSW
 SG_ VoltageCal_state_request : 24|3@1+ (1,0) [0|6] ""  BMS_MV,RTE
 SG_ TemperatureCal_state_request : 32|3@1+ (1,0) [0|6] ""  BMS_MV,RTE
 SG_ CurrentCal_state_request : 16|3@1+ (1,0) [0|6] ""  BMS_MV,RTE
 SG_ Calibration_request : 0|3@1+ (1,0) [0|6] ""  BMS_MV,RTE
 SG_ VoltageCal_MeasTime : 48|5@1+ (100,0) [0|3000] "ms"  BMS_MV,RTE
 SG_ TemperatureCal_MeasTime : 56|5@1+ (100,0) [0|3000] "ms"  BMS_MV,RTE
 SG_ CurrentCal_MeasTime : 40|5@1+ (100,0) [0|3000] "ms"  BMS_MV,RTE

BO_ 2147483780 EMB_DBG_CELL_TEMPERATURE: 12 BMS_MV
 SG_ CellTemperature_4 : 64|16@1- (0.1,0) [-3276.8|3276.7] "�C"  EMB_SSW,RTE
 SG_ CellTemperature_3 : 48|16@1- (0.1,0) [-3276.8|3276.7] "�C"  EMB_SSW,RTE
 SG_ CellTemperature_2 : 32|16@1- (0.1,0) [-3276.8|3276.7] "�C"  EMB_SSW,RTE
 SG_ CellTemperature_1 : 16|16@1- (0.1,0) [-3276.8|3276.7] "�C"  EMB_SSW,RTE
 SG_ CellTemperature_0 : 0|16@1- (0.1,0) [-3276.8|3276.7] "�C"  EMB_SSW,RTE

BO_ 2147483778 EMB_DBG_ERROR_EMB: 8 BMS_MV
 SG_ ERR_RSV8 : 28|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV7 : 24|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV6 : 20|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV5 : 16|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV4 : 12|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV3 : 8|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV2 : 4|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_RSV1 : 0|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE

BO_ 2147483776 EMB_DBG_DEBUG_2: 8 BMS_MV
 SG_ EMBDebugData_32_2 : 32|32@1+ (1,0) [0|4294967295] ""  EMB_SSW,RTE
 SG_ EMBDebugData_32_1 : 0|32@1+ (1,0) [0|4294967294] ""  EMB_SSW,RTE

BO_ 2147483772 EMB_DBG_SW_REVISION: 8 BMS_MV
 SG_ svn_revison : 48|16@1+ (1,0) [0|65534] ""  EMB_SSW,RTE
 SG_ patch : 32|16@1+ (1,0) [0|65534] ""  EMB_SSW,RTE
 SG_ minor : 16|16@1+ (1,0) [0|65534] ""  EMB_SSW,RTE
 SG_ major : 0|16@1+ (1,0) [0|65534] ""  EMB_SSW,RTE

BO_ 2147483774 EMB_DBG_DEBUG_1: 8 BMS_MV
 SG_ EMBDebugData_16_4 : 48|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ EMBDebugData_16_3 : 32|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ EMBDebugData_16_2 : 16|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE
 SG_ EMBDebugData_16_1 : 0|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE

BO_ 2147483749 EMB_DBG_CONTROL: 8 EMB_SSW
 SG_ DeepSleep_Request : 63|1@1+ (1,0) [0|1] ""  RTE,BMS_MV
 SG_ SOCBmsBasic_Reset : 14|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ EMBSimulationMode_enable : 15|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ DEM_FaultReset : 13|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ NVM_erase : 12|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ PowerDown_request : 11|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ PSWclose_request : 10|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ PSWmanualMode_enable : 9|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ Fan_enable : 8|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ Fan02Duty_set : 32|8@1+ (1,0) [0|100] ""  BMS_MV,RTE
 SG_ Fan01Duty_set : 24|8@1+ (1,0) [0|100] ""  BMS_MV,RTE
 SG_ EMF_FET_CD_enable : 7|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ V_SCP_C_CHECK_CTRL_set : 6|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ GDRV_enable : 5|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ DMOS_enable : 4|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ CMOS_enable : 3|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ SCP_factor_data : 16|8@1+ (1,0) [0|254] ""  BMS_MV,RTE
 SG_ SCP_factor_set : 2|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ QT2Toggle_set : 1|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ EMBDbgMode_enable : 0|1@1+ (1,0) [0|1] ""  RTE,BMS_MV

BO_ 2147483768 EMB_DBG_STATUS: 8 BMS_MV
 SG_ DummySwitch_active : 63|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ EMBSimulationMode_active : 47|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ PowerDown_enabled : 43|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ PSWctlState : 24|8@1+ (1,0) [0|255] ""  EMB_SSW,RTE
 SG_ PSWmanualMode_active : 42|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ ERR_TLE9180_02 : 41|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ ERR_TLE9180_01 : 40|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ V_SCP_C_CHECK_CTRL_state : 38|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ EMF_FET_CD_active : 39|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ GDRV_active : 37|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ DMOS_active : 36|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ CMOS_active : 35|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ SCP_active : 34|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ QT2Toggle_active : 33|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ EMBDbgMode_active : 32|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ SCP_factor : 16|8@1+ (1,0) [0|254] ""  EMB_SSW,RTE
 SG_ StatusInfo : 0|16@1+ (1,0) [0|65535] ""  EMB_SSW,RTE

BO_ 2147483753 EMB_DBG_RTC_SET: 8 EMB_SSW
 SG_ Set_RTCAlarmInMinutes : 2|14@1+ (1,0) [1|10079] ""  BMS_MV,RTE
 SG_ Set_RTCAlarm : 1|1@1+ (1,0) [0|1] ""  BMS_MV,RTE
 SG_ Set_Second : 56|8@1+ (1,0) [0|59] ""  RTE,BMS_MV
 SG_ Set_Minute : 48|8@1+ (1,0) [0|59] ""  RTE,BMS_MV
 SG_ Set_Hour : 40|8@1+ (1,0) [0|23] ""  RTE,BMS_MV
 SG_ Set_Day : 32|8@1+ (1,0) [0|31] ""  RTE,BMS_MV
 SG_ Set_Month : 24|8@1+ (1,0) [1|12] ""  RTE,BMS_MV
 SG_ Set_Year : 16|8@1+ (1,0) [0|99] ""  RTE,BMS_MV
 SG_ Set_RTC : 0|1@1+ (1,0) [0|1] ""  BMS_MV,RTE

BO_ 2147483756 EMB_DBG_CRASH_LEVEL: 8 BMS_MV
 SG_ CrashDutyT1 : 16|8@1+ (1,0) [0|100] ""  EMB_SSW,RTE
 SG_ CrashPeriod : 8|8@1+ (1,0) [0|100] ""  RTE,EMB_SSW
 SG_ CrashLevel : 0|8@1+ (1,0) [0|3] ""  RTE,EMB_SSW

BO_ 2147483751 EMB_DBG_REQUEST: 8 EMB_SSW
 SG_ RequestType : 0|8@1+ (1,0) [0|255] ""  RTE,BMS_MV
 SG_ RequestData : 8|56@1+ (1,0) [0|0] ""  RTE,BMS_MV

BO_ 2147483764 EMB_DBG_RESPONSE: 8 BMS_MV
 SG_ ResponseType : 0|8@1+ (1,0) [0|255] ""  RTE,EMB_SSW
 SG_ ResponseData : 8|56@1+ (1,0) [0|0] ""  RTE,EMB_SSW

BO_ 2147483750 EMB_DBG_CELLVOLTAGES0: 8 BMS_MV
 SG_ CellVoltage0 : 0|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage1 : 16|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage2 : 32|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage3 : 48|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW

BO_ 2147483752 EMB_DBG_CELLVOLTAGES1: 8 BMS_MV
 SG_ CellVoltage4 : 0|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage5 : 16|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage6 : 32|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage7 : 48|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW

BO_ 2147483754 EMB_DBG_CELLVOLTAGES2: 8 BMS_MV
 SG_ CellVoltage8 : 0|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage9 : 16|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage10 : 32|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW
 SG_ CellVoltage11 : 48|16@1+ (0.001,0) [0|65.535] "V"  RTE,EMB_SSW

BO_ 2147483758 EMB_DBG_CURRENT: 8 BMS_MV
 SG_ HallSensorCurrent : 0|32@1- (1,0) [-2147483648|2147483646] "mA"  EMB_SSW,RTE
 SG_ ShuntCurrent : 32|32@1- (1,0) [-2147483648|2147483646] "mA"  EMB_SSW,RTE

BO_ 2147483770 EMB_DBG_TEMPERATURE: 12 BMS_MV
 SG_ Temp_DMOS01 : 0|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW
 SG_ Temp_DMOS02 : 16|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW
 SG_ Temp_CMOS01 : 32|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW
 SG_ Temp_CMOS02 : 48|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW
 SG_ TempFanIn : 64|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW
 SG_ TempFanOut : 80|16@1- (0.1,0) [-3276.8|3276.7] "�C"  RTE,EMB_SSW

BO_ 2147483748 EMB_DBG_VOLTAGES: 8 BMS_MV
 SG_ StackVoltage : 48|16@1+ (0.001,0) [0|65.534] "V"  EMB_SSW,RTE
 SG_ LinkVoltage : 32|16@1+ (0.001,0) [0|65.534] "V"  EMB_SSW,RTE
 SG_ Diag_12VADC1 : 0|16@1+ (0.001,0) [0|65.534] "V"  RTE,EMB_SSW
 SG_ Diag_12VADC2 : 16|16@1+ (0.001,0) [0|65.534] "V"  RTE,EMB_SSW

BO_ 2147483762 EMB_DBG_FAN_DRV: 8 BMS_MV
 SG_ Fan02Rpm : 40|16@1+ (1,0) [0|65534] "rpm"  EMB_SSW,RTE
 SG_ Fan02Duty_out : 16|8@1+ (1,0) [0|100] "%"  EMB_SSW,RTE
 SG_ Fan_active : 0|1@1+ (1,0) [0|1] ""  RTE,EMB_SSW
 SG_ Fan01Duty_out : 8|8@1+ (1,0) [0|100] "%"  RTE,EMB_SSW
 SG_ Fan01Rpm : 24|16@1+ (1,0) [0|65534] "rpm"  RTE,EMB_SSW

BO_ 2147483760 EMB_DBG_ERROR_TPT: 8 BMS_MV
 SG_ ERR_FUSE : 56|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_UVP : 4|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_SCP : 36|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_OVP : 0|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_MOT : 40|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_MFF : 44|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_GDF : 52|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_DUT : 24|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_DOT : 20|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_DOC : 32|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_CUT : 16|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_CUB : 8|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_COT : 12|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_COC : 28|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE
 SG_ ERR_AFF : 48|4@1+ (1,0) [0|4] ""  EMB_SSW,RTE

BO_ 2147483766 EMB_DBG_RTC: 12 BMS_MV
 SG_ RTCAlarm_Triggered : 49|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ RTCAlarm_Second : 80|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ RTCAlarm_Minute : 72|8@1+ (1,0) [0|59] ""  EMB_SSW,RTE
 SG_ RTCAlarm_Hour : 64|8@1+ (1,0) [0|23] ""  EMB_SSW,RTE
 SG_ RTCAlarm_Enabled : 48|1@1+ (1,0) [0|1] ""  EMB_SSW,RTE
 SG_ RTCAlarm_Day : 56|8@1+ (1,0) [0|7] ""  EMB_SSW,RTE
 SG_ Year : 0|8@1+ (1,0) [0|99] ""  RTE,EMB_SSW
 SG_ Month : 8|8@1+ (1,0) [0|12] ""  RTE,EMB_SSW
 SG_ Day : 16|8@1+ (1,0) [0|31] ""  RTE,EMB_SSW
 SG_ Hour : 24|8@1+ (1,0) [0|23] ""  RTE,EMB_SSW
 SG_ Minute : 32|8@1+ (1,0) [0|59] ""  RTE,EMB_SSW
 SG_ Second : 40|8@1+ (1,0) [0|59] ""  RTE,EMB_SSW



CM_ "EVCANFD_V14.01F Generated by BusNet_NG_Produktiv 9.17.6";
CM_ BU_ BMS_MV "Batterie 48V MQBw_Baseline";
CM_ BU_ EMB_SSW "embeddeers service software";
CM_ SG_ 2645185858 ERR_CFet_ErrDigOut "Bitmap of digital output errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrCurSnsAmp3 "Bitmap of current sense amp errors for AMP 3 (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrCurSnsAmp12 "Bitmap of current sense amp errors for AMP 1 and 2 (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrSpiAndCfg "Bitmap of SPI errors and config errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrOutStage "Bitmap of power stage errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrPattern "Bitmap of pattern violation errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrShortCircuit "Bitmap of short circuit errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrShutDown "Bitmap of shutdown errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrExternal "Bitmap of external errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrInternal2 "Bitmap of internal errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrInternal1 "Bitmap of internal errors (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_SpecialEvent "Bitmap of special events (C-Fet)";
CM_ SG_ 2645185858 ERR_CFet_ErrOverview "Cumulative bitmap of all errors (C-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrDigOut "Bitmap of digital output errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrCurSnsAmp3 "Bitmap of current sense amp errors for AMP 3 (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrCurSnsAmp12 "Bitmap of current sense amp errors for AMP 1 and 2 (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrSpiAndCfg "Bitmap of SPI errors and config errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrOutStage "Bitmap of power stage errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrPattern "Bitmap of pattern violation errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrShortCircuit "Bitmap of short circuit errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrShutDown "Bitmap of shutdown errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrExternal "Bitmap of external errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrInternal2 "Bitmap of internal errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrInternal1 "Bitmap of internal errors (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_SpecialEvent "Bitmap of special events (D-Fet)";
CM_ SG_ 2645185858 ERR_DFet_ErrOverview "Cumulative bitmap of all errors (D-Fet)";
CM_ SG_ 2147483773 hw_version "Hardware Version Number";
CM_ SG_ 2147487752 s_IMaxDischarge "Max discharging current for power suggestion";
CM_ SG_ 2147487752 s_IMaxCharge "Max charging current for power suggestion";
CM_ SG_ 2147487752 s_UMinDischarge "Min discharging voltage for power suggestion";
CM_ SG_ 2147487752 s_UMaxCharge "Max charging voltage for power suggestion";
CM_ SG_ 2147487752 qQBatTotal_mu8 "Qualifier for QBatTotal_mu16";
CM_ SG_ 2147487752 QBatTotal_mu16 "Calculated battery capacity value";
CM_ SG_ 2147487752 cpa_bOCVReady_mb "Indication if open circuit voltage is ready.";
CM_ SG_ 2147487752 przSOCOCV_mu16 "SOC based on open circuit voltage calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellMin "Minimum calculated SOC value of all cells.";
CM_ SG_ 2147487752 s_przSOCCellMax "Maximum calculated SOC value of all cells.";
CM_ SG_ 2147487752 qprzBatSOC_mu8 "Qualifier for soc_przCellSOCMax_mu16, soc_przCellSOCMin_mu16 and soc_przBatSOC_mu16.";
CM_ SG_ 2147487752 qprzSysSOCEP_mu8 "Qualifier for s_przSOCBMS";
CM_ SG_ 2147487752 s_przSOCCellArray_9 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_8 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_7 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_6 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_5 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_4 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_3 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_2 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_11 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_10 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_1 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 s_przSOCCellArray_0 "Cell SOC of cell (index) calculated by the BCK";
CM_ SG_ 2147487752 przBatSOC_mu16 "Battery SOC calculated by the BCK";
CM_ SG_ 2147487752 SOCBmsBasicDyn "SOC calculated by TP ASW based on actual average filtered cell voltage";
CM_ SG_ 2147487752 SOCBmsBasic "SOC calculated by TP ASW based on OCV and current integration";
CM_ SG_ 2147487752 s_przSOCBMS "REQ-608689: SOC calculated by the BCK (also on VCAN signal BMS_MV_SOC_HiRes)";
CM_ SG_ 2147487746 UBatStack "Battery Cell Stack Voltage measured by AFE from pin PWR2, to be used for plausibility check with s_UBat";
CM_ SG_ 2147487746 EOCBState "End Of Cell Balancing State";
CM_ SG_ 2147487746 s_bShtDwnPrevHwResetCycle "indicates a complete previous shutdown (all NVM data written)";
CM_ SG_ 2147487746 s_bal_bCalcCompl "State of the cell balancing request from BCK";
CM_ SG_ 2147487746 s_tBalWakeUp_min "Time until next BMS wakeup for cell balancing monitoring and control.";
CM_ SG_ 2147487746 s_tBalTimeArray_min_11 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_10 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_9 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_8 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_7 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_6 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_5 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_4 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_3 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_2 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_1 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_tBalTimeArray_min_0 "Cell Balancing Duration of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_9 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_8 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_7 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_6 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_5 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_4 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_3 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_2 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_11 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_10 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_1 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusQualifierArray_0 "Balancing Status Qualifier of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_11 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_10 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_9 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_8 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_7 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_6 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_5 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_4 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_3 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_2 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_1 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 s_blBalStatusArray_0 "Cell Balancing Status of Cell (index)";
CM_ SG_ 2147487746 OMM_State "Actual state of OMM state machine";
CM_ SG_ 2147487746 AliveCounter "Rolling counter (0 .. 15) that can be used for debugging in order to indicate if the software is running stable";
CM_ SG_ 2147487746 TimeSinceStart "Absoulte time since start of the software (i.e. start of the scheduler)";
CM_ SG_ 2147487746 KL75 "REQ-608757: same as s_blZasKl75, KL75 signal as received through signal Motor_14::MO_Kl_75";
CM_ SG_ 2147487746 KL15 "REQ-608847: same as s_blZasKl15, KL15 signal as received through signal Klemmen_Status_01::KST_KL_15";
CM_ SG_ 2147487746 s_blInitBMS "REQ-608751: Init status";
CM_ SG_ 2147487746 vVehicle "REQ-608849: same as s_vVeh_kmh, Vehicle Speed as received through signal ESP_21::ESP_v_Signal";
CM_ SG_ 2147487746 UCellLpAvg "Average value of all 12 filtered Cell Voltages";
CM_ SG_ 2147487746 UCellMax "Unfiltered maximum Cell Voltage";
CM_ SG_ 2147487746 UCellDelta "Unfiltered difference between minimum and maximum Cell Voltage";
CM_ SG_ 2147487746 UCellMin "Unfiltered minimum Cell Voltage";
CM_ SG_ 2147487746 s_blExtRangeReq "REQ-608749: First start - extended power range request";
CM_ SG_ 2147487746 SOCColdCrank "REQ-425841: Min. Cold Crank SOC calculated from s_przColdCrankSOC�and s_przMinSOC (same as BMS_MV_SOC_Kaltstart)";
CM_ SG_ 2147487746 enModeReq_mu8 "INT_REQ2072
INT_REQ2297: Modified Target Mode Request -> actual mode including EMERGENCY_OFF reflected";
CM_ SG_ 2147487746 s_valActualModeInt "REQ-608715: Actual operation state of the battery (for internal use)";
CM_ SG_ 2147487746 s_valTargetModeBMS "REQ-608865: Target Mode of the Battery as requested from the vehicle though MVK_01::
MVK_BMS_Sollmodus";
CM_ SG_ 2147487746 s_valActualModeBMS "REQ-608713: Actual operation state of the battery (for communication to vehicle)";
CM_ SG_ 2147487750 s_PminColdCranking "REQ-608843: required cold start power";
CM_ SG_ 2147487750 s_PCool "REQ-608654: current cooling/heating power";
CM_ SG_ 2147487750 s_PColdCrankingSupport "REQ-608845: cold start support power from 12V power net";
CM_ SG_ 2147487750 s_PBatColdCrank "REQ-608699: Minimum power that the 48V battery must be able to provide for cold start
";
CM_ SG_ 2147487750 SpeedFan "Fan Rotor Speed";
CM_ SG_ 2147487750 PwmFan2 "REQ-608701: same as s_przFanPWM, Fan2 PWM Duty Cycle Command";
CM_ SG_ 2147487750 PwmFan1 "REQ-608649: same as s_przFanPWM,�Fan1 PWM Duty Cycle Command";
CM_ SG_ 2147487750 FanEnableRequest "Indicator if Thermal Management by Fan Operation is active
";
CM_ SG_ 2147487750 HeatingEnabled "Indicator if Thermal Management by Heating is active";
CM_ SG_ 2147487750 CoolingEnabled "Indicator if Thermal Management by Cooling is active";
CM_ SG_ 2147487750 TCellMin "Minimum unfiltered Cell Temperature";
CM_ SG_ 2147487750 TCellMax "Maximum unfiltered Cell Temperature";
CM_ SG_ 2147487750 TBat "REQ-608643: same as s_TBat_Celsius, Current Battery Temperature (calculated by the TP ASW)";
CM_ SG_ 2147487750 s_tSleepTime "not supported in X100/X110";
CM_ SG_ 2147487750 s_valErrQAHCount "REQ-608887: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_QDischarge "REQ-608651: Ampere-hours counter in discharge direction
Known issue with signal roll-over at 65535. Only relvant for DebugCAN signal. 
No issue with signal passed to BCK";
CM_ SG_ 2147487750 s_QCharge "REQ-608649: Ampere-hours counter in charge direction
Known issue with signal roll-over at 65535. Only relvant for DebugCAN signal. 
No issue with signal passed to BCK";
CM_ SG_ 2147487750 s_TOutlet_Celsius "REQ-608647: Filtered air cooling outlet temperature";
CM_ SG_ 2147487750 s_TInlet_Celsius "REQ-608645: Filtered air cooling inlet temperature";
CM_ SG_ 2147487750 s_valErrTCellArray_4 "REQ-608883: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_valErrTCellArray_3 "REQ-608883: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_valErrTCellArray_2 "REQ-608883: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_valErrTCellArray_1 "REQ-608883: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_valErrTCellArray_0 "REQ-608883: Qualifier, default 0 for BCK, not further supported";
CM_ SG_ 2147487750 s_valErrTOutlet "REQ-608891: Qualifier, default 0 for BCK, not further supported in X100";
CM_ SG_ 2147487750 s_valErrTInlet "REQ-608889: Qualifier, default 0 for BCK, not further supported in X100";
CM_ SG_ 2147487750 s_TBat_Celsius "REQ-608643: Battery Temperature calculated by BCK";
CM_ SG_ 2147487750 s_TDeltaCell "REQ-608641: Difference between maximum and minimum filtered Cell Temperature";
CM_ SG_ 2147487750 s_TCellMin_Celsius "REQ-608635: Minimum filtered Cell Temperature";
CM_ SG_ 2147487750 s_TCellMax_Celsius "REQ-608637: Maximum filtered Cell Temperature";
CM_ SG_ 2147487750 s_TCellArray_Celsius_4 "REQ-608633: Filtered Cell Temperature - Sensor 5 of 5";
CM_ SG_ 2147487750 s_TCellArray_Celsius_3 "REQ-608633: Filtered Cell Temperature - Sensor 4 of 5";
CM_ SG_ 2147487750 s_TCellArray_Celsius_2 "REQ-608633: Filtered Cell Temperature - Sensor 3 of 5";
CM_ SG_ 2147487750 s_TCellArray_Celsius_1 "REQ-608633: Filtered Cell Temperature - Sensor 2 of 5";
CM_ SG_ 2147487750 s_TCellArray_Celsius_0 "REQ-608633: Filtered Cell Temperature - Sensor 1 of 5";
CM_ SG_ 2147487750 s_TOutletRaw_Celsius "REQ-608611: Unfiltered air cooling outlet temperature";
CM_ SG_ 2147487750 s_TInletRaw_Celsius "REQ-608609: Unfiltered air cooling inlet temperature";
CM_ SG_ 2147487750 s_TCellArrayRaw_Celsius_4 "REQ-608613: Unfiltered Cell Temperature - Sensor 5 of 5";
CM_ SG_ 2147487750 s_TCellArrayRaw_Celsius_3 "REQ-608613: Unfiltered Cell Temperature - Sensor 4 of 5";
CM_ SG_ 2147487750 s_TCellArrayRaw_Celsius_2 "REQ-608613: Unfiltered Cell Temperature - Sensor 3 of 5";
CM_ SG_ 2147487750 s_TCellArrayRaw_Celsius_1 "REQ-608613: Unfiltered Cell Temperature - Sensor 2 of 5";
CM_ SG_ 2147487750 s_TCellArrayRaw_Celsius_0 "REQ-608613: Unfiltered Cell Temperature - Sensor 1 of 5";
CM_ SG_ 2147487748 F_FUSE_FAILURE "Fuse failure Level 4";
CM_ SG_ 2147487748 ser_qbEbatOK_mu8 "Qualifier for signal ser_bEbatOK_mb";
CM_ SG_ 2147487748 ser_bEbatOK_mb "State of the safety energy reserve (from BCK)";
CM_ SG_ 2147487748 DBGCAN_TxQueueFullCounter_XL "Debug CAN Tx Message Queue Overflow Counter for 64 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_TxQueueFullCounter_L "Debug CAN Tx Message Queue Overflow Counter for 32 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_TxQueueFullCounter_M "Debug CAN Tx Message Queue Overflow Counter for 16 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_TxQueueFullCounter_S "Debug CAN Tx Message Queue Overflow Counter for 8 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_RxQueueFullCounter_XL "Debug CAN Rx Message Queue Overflow Counter for 64 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_RxQueueFullCounter_L "Debug CAN Rx Message Queue Overflow Counter for 32 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_RxQueueFullCounter_M "Debug CAN Rx Message Queue Overflow Counter for 16 Byte DLC messages";
CM_ SG_ 2147487748 DBGCAN_RxQueueFullCounter_S "Debug CAN Rx Message Queue Overflow Counter for 8 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_TxQueueFullCounter_XL "Vehicle CAN Tx Message Queue Overflow Counter for 64 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_TxQueueFullCounter_S "Vehicle CAN Tx Message Queue Overflow Counter for 8 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_TxQueueFullCounter_M "Vehicle CAN Tx Message Queue Overflow Counter for 16 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_TxQueueFullCounter_L "Vehicle CAN Tx Message Queue Overflow Counter for 32 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_RxQueueFullCounter_XL "Vehicle CAN Rx Message Queue Overflow Counter for 64 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_RxQueueFullCounter_L "Vehicle CAN Rx Message Queue Overflow Counter for 32 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_RxQueueFullCounter_M "Vehicle CAN Rx Message Queue Overflow Counter for 16 Byte DLC messages";
CM_ SG_ 2147487748 VCAN_RxQueueFullCounter_S "Vehicle CAN Rx Message Queue Overflow Counter for 8 Byte DLC messages";
CM_ SG_ 2147487748 TimeSinceLastTargetModeMsg "Time since last reception of new MVK_01 message";
CM_ SG_ 2147487748 TargetModeMessageReceived "Indicator from PDUR that message MVK_01 was received";
CM_ SG_ 2147487748 F_PRECHARGE_TO "Precharging TimeOut - Precharging takes too long";
CM_ SG_ 2147487748 s_blEmergencyOperationModeReq "Indicator if Power reduction is required (mapped to BMS_MV_Leistungsreduzierung)";
CM_ SG_ 2147487748 s_valCrashShutdownSWReq "same as Airbag_01::AB_Deaktivierung_HV";
CM_ SG_ 2147487748 s_valEResEmergency "Indicator for Energy Reserve (mapped to BMS_MV_Fehler_Notfallreserve)";
CM_ SG_ 2147487748 s_valDeepDischarge "Deep Discharge Level";
CM_ SG_ 2147487748 s_cntDeepDischarge "Counts number of Deep Discharge Events";
CM_ SG_ 2147487748 s_blErrBatteryOffReq "Indicator that Battery Deactivation is due to Fault is pending";
CM_ SG_ 2147487748 s_blErrBatteryOff "Indicator that Battery is deactivated due to Fault";
CM_ SG_ 2147487748 s_valErrStatusBMS "Indicates any active fault";
CM_ SG_ 2147487748 s_valErrAirflow "Status Air Flow Thermal Management";
CM_ SG_ 2147487748 F_FAN_ROTOR_LOCKED "Cooling System Fan Rotor is Locked";
CM_ SG_ 2147487748 F_AIRDUCT_BLOCKED "Cooling System Airduct is Blocked";
CM_ SG_ 2147487748 F_DOC_L3 "Discharging Overcurrent Level 3";
CM_ SG_ 2147487748 F_DOC_L2 "Discharging Overcurrent Level 2 (not implemented)";
CM_ SG_ 2147487748 F_DOC_L1 "Discharging Overcurrent Level 1";
CM_ SG_ 2147487748 F_COC_L3 "Charging Overcurrent Level 3";
CM_ SG_ 2147487748 F_COC_L2 "Charging Overcurrent Level 2";
CM_ SG_ 2147487748 F_COC_L1 "Charging Overcurrent Level 1";
CM_ SG_ 2147487748 F_SOC_DD_L3 "Deep Discharge Level 3";
CM_ SG_ 2147487748 F_SOC_DD_L2 "Deep Discharge Level 2";
CM_ SG_ 2147487748 F_SOC_DD_L1 "Deep Discharge Level 1";
CM_ SG_ 2147487748 F_MVK_01_TO "CAN Message MVK_01 Time Out - no s_valTargetModeBMS available";
CM_ SG_ 2147487748 F_CRASH_CAN_IMPL "SW Crash Signal received with implausible value";
CM_ SG_ 2147487748 F_CRASH_CAN_ERRVAL "SW Crash Signal received with Error Value";
CM_ SG_ 2147487748 F_CRASH_L2 "SW Crash Level 2 detected";
CM_ SG_ 2147487748 F_CRASH_L1 "SW Crash Level 1 detected";
CM_ SG_ 2147487748 F_GDR_FAILURE "MOSFET Gate Driver Failure";
CM_ SG_ 2147487748 F_AFE_FAILURE "Analog Front-End Failure";
CM_ SG_ 2147487748 F_MOSFET_FAILURE "MOSFET Failure - Leakage Current Detected";
CM_ SG_ 2147487748 F_MOSFET_OT_L3 "MOSFET Overtemperature Level 3";
CM_ SG_ 2147487748 F_MOSFET_OT_L2 "MOSFET Overtemperature Level 2";
CM_ SG_ 2147487748 F_MOSFET_OT_L1 "MOSFET Overtemperature Level 1";
CM_ SG_ 2147487748 F_SCP "Short-Circuit Protection";
CM_ SG_ 2147487748 F_DUT_L4 "Discharging Undertemperature Level 4 (not implemented)";
CM_ SG_ 2147487748 F_DUT_L3 "Discharging Undertemperature Level 3 (not implemented)";
CM_ SG_ 2147487748 F_DUT_L2 "Discharging Undertemperature Level 2 (not implemented)";
CM_ SG_ 2147487748 F_DUT_L1 "Discharging Undertemperature Level 1";
CM_ SG_ 2147487748 F_DOT_L4 "Discharging Overtemperature Level 4 (not implemented)";
CM_ SG_ 2147487748 F_DOT_L3 "Discharging Overtemperature Level 3";
CM_ SG_ 2147487748 F_DOT_L2 "Discharging Overtemperature Level 2";
CM_ SG_ 2147487748 F_DOT_L1 "Discharging Overtemperature Level 1";
CM_ SG_ 2147487748 F_CUT_L4 "Charging Undertemperature Level 4 (not implemented)";
CM_ SG_ 2147487748 F_CUT_L3 "Charging Undertemperature Level 3";
CM_ SG_ 2147487748 F_CUT_L2 "Charging Undertemperature Level 2";
CM_ SG_ 2147487748 F_CUT_L1 "Charging Undertemperature Level 1";
CM_ SG_ 2147487748 F_COT_L4 "Charging Overtemperature Level 4 (not implemented)";
CM_ SG_ 2147487748 F_COT_L3 "Charging Overtemperature Level 3";
CM_ SG_ 2147487748 F_COT_L2 "Charging Overtemperature Level 2";
CM_ SG_ 2147487748 F_COT_L1 "Charging Overtemperature Level 1";
CM_ SG_ 2147487748 F_CELL_UNBALANCE_L4 "Cell Voltages unbalanced";
CM_ SG_ 2147487748 F_UVP_L4 "Undervoltage Protection Level 4";
CM_ SG_ 2147487748 F_UVP_L3 "Undervoltage Protection Level 3";
CM_ SG_ 2147487748 F_UVP_L2 "Undervoltage Protection Level 2";
CM_ SG_ 2147487748 F_UVP_L1 "Undervoltage Protection Level 1";
CM_ SG_ 2147487748 F_OVP_L4 "Overvoltage Protection Level 4";
CM_ SG_ 2147487748 F_OVP_L3 "Overvoltage Protection Level 3";
CM_ SG_ 2147487748 F_OVP_L2 "Overvoltage Protection Level 2";
CM_ SG_ 2147487748 F_OVP_L1 "Overvoltage Protection Level 1";
CM_ SG_ 2147487744 s_valErrUCellMinMax "REQ-608881: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_UCellMin "REQ-608631: Minimum filtered Cell Voltage";
CM_ SG_ 2147487744 s_UCellMax "REQ-608629: Maximum filtered Cell Voltage";
CM_ SG_ 2147487744 s_valErrUCellArray_11 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_10 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_9 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_8 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_7 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_6 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_5 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_4 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_3 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_2 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_1 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_valErrUCellArray_0 "REQ-608879: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_UCellArray_11 "REQ-608627: Filtered Cell Voltage - Cell 12 of 12";
CM_ SG_ 2147487744 s_UCellArray_10 "REQ-608627: Filtered Cell Voltage - Cell 11 of 12";
CM_ SG_ 2147487744 s_UCellArray_9 "REQ-608627: Filtered Cell Voltage - Cell 10 of 12";
CM_ SG_ 2147487744 s_UCellArray_8 "REQ-608627: Filtered Cell Voltage - Cell 9 of 12";
CM_ SG_ 2147487744 s_UCellArray_7 "REQ-608627: Filtered Cell Voltage - Cell 8 of 12";
CM_ SG_ 2147487744 s_UCellArray_6 "REQ-608627: Filtered Cell Voltage - Cell 7 of 12";
CM_ SG_ 2147487744 s_UCellArray_5 "REQ-608627: Filtered Cell Voltage - Cell 6 of 12";
CM_ SG_ 2147487744 s_UCellArray_4 "REQ-608627: Filtered Cell Voltage - Cell 5 of 12";
CM_ SG_ 2147487744 s_UCellArray_3 "REQ-608627: Filtered Cell Voltage - Cell 4 of 12";
CM_ SG_ 2147487744 s_UCellArray_2 "REQ-608627: Filtered Cell Voltage - Cell 3 of 12";
CM_ SG_ 2147487744 s_UCellArray_1 "REQ-608627: Filtered Cell Voltage - Cell 2 of 12";
CM_ SG_ 2147487744 s_UCellArray_0 "REQ-608627: Filtered Cell Voltage - Cell 1 of 12";
CM_ SG_ 2147487744 s_PBat_mi32 "INT_REQ2321: Calculated Battery Power";
CM_ SG_ 2147487744 s_valErrUKl40 "REQ-608877: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_UKl40 "REQ-608625: Filtered DC Link Voltage KL40";
CM_ SG_ 2147487744 s_valErrUBat "REQ-608875: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_Ubat "REQ-608623: Filtered Battery Voltage (based on added cell voltages)";
CM_ SG_ 2147487744 s_valErrIBat "REQ-608873: Qualifier, default 0 for BCK, not supported";
CM_ SG_ 2147487744 s_IBat "REQ-6068145: Filtered Battery Current";
CM_ SG_ 2147487744 s_UCellArrayRaw_11 "REQ-608607: Unfiltered Cell Voltage - Cell 12 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_10 "REQ-608607: Unfiltered Cell Voltage - Cell 11 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_9 "REQ-608607: Unfiltered Cell Voltage - Cell 10 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_8 "REQ-608607: Unfiltered Cell Voltage - Cell 9 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_7 "REQ-608607: Unfiltered Cell Voltage - Cell 8 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_6 "REQ-608607: Unfiltered Cell Voltage - Cell 7 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_5 "REQ-608607: Unfiltered Cell Voltage - Cell 6 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_4 "REQ-608607: Unfiltered Cell Voltage - Cell 5 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_3 "REQ-608607: Unfiltered Cell Voltage - Cell 4 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_2 "REQ-608607: Unfiltered Cell Voltage - Cell 3 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_1 "REQ-608607: Unfiltered Cell Voltage - Cell 2 of 12";
CM_ SG_ 2147487744 s_UCellArrayRaw_0 "REQ-608607: Unfiltered Cell Voltage - Cell 1 of 12";
CM_ SG_ 2147487744 s_UKl40Raw "REQ-608605: Unfiltered DC Link Voltage KL40";
CM_ SG_ 2147487744 s_UbatRaw "REQ-608603: Unfiltered Battery Voltage (based on added cell voltages)";
CM_ SG_ 2147487744 s_IBatRaw "REQ-608599: Unfiltered Battery Current";
CM_ SG_ 2147483778 ERR_RSV8 "Error Reserved 8";
CM_ SG_ 2147483778 ERR_RSV7 "Error Reserved 7";
CM_ SG_ 2147483778 ERR_RSV6 "Error Reserved 6";
CM_ SG_ 2147483778 ERR_RSV5 "Error Reserved 5";
CM_ SG_ 2147483778 ERR_RSV4 "Error Reserved 4";
CM_ SG_ 2147483778 ERR_RSV3 "Error Reserved 3";
CM_ SG_ 2147483778 ERR_RSV2 "Error Reserved 2";
CM_ SG_ 2147483778 ERR_RSV1 "Error Reserved 1";
CM_ SG_ 2147483772 svn_revison "Software Version SVN Revision";
CM_ SG_ 2147483772 patch "Software Version Patch Number";
CM_ SG_ 2147483772 minor "Software Version Minor Number";
CM_ SG_ 2147483772 major "Software Version Major Number";
CM_ SG_ 2147483749 DeepSleep_Request "Force BMS to be transferred to Deep Sleep State";
CM_ SG_ 2147483749 SOCBmsBasic_Reset "when set SOCBmsBasic is reset based on actual average cell voltage";
CM_ SG_ 2147483749 DEM_FaultReset "Set and then clear to reset permanent faults as defined in Fault_Table.xlsx";
CM_ SG_ 2147483749 PowerDown_request "When set, an inteded power down of the software is requested. Software will wait for CAN communication to stop and the power down to SLEEP mode";
CM_ SG_ 2147483749 Fan_enable "Indicator if Fan is enabled";
CM_ SG_ 2147483749 Fan02Duty_set "not used for A sample";
CM_ SG_ 2147483749 Fan01Duty_set "Fan 01 Pwm Duty Cycle Set Point";
CM_ SG_ 2147483749 EMF_FET_CD_enable "When set, the debug mode is enabled (not required for operation of VW SW)";
CM_ SG_ 2147483768 PowerDown_enabled "Indicates that the power down sequence has been entered after power down request";
CM_ SG_ 2147483768 EMBDbgMode_active "Indicates, if the debug mode is active";
CM_ SG_ 2147483762 Fan02Rpm "not used for A sample";
CM_ SG_ 2147483762 Fan02Duty_out "not used for A sample";
CM_ SG_ 2147483762 Fan_active "Indicator for Fan Activity";
CM_ SG_ 2147483762 Fan01Duty_out "Fan 01 Pwm Duty Cycle";
CM_ SG_ 2147483762 Fan01Rpm "Fan 01 rotor speed";
CM_ SG_ 2147483760 ERR_FUSE "Fuse blown";
CM_ SG_ 2147483760 ERR_UVP "Cell Under Voltage Protection";
CM_ SG_ 2147483760 ERR_SCP "Short Circuit Protection";
CM_ SG_ 2147483760 ERR_OVP "Cell Over Voltage Protection";
CM_ SG_ 2147483760 ERR_MOT "MOSFET Over Temperature";
CM_ SG_ 2147483760 ERR_MFF "MOSFET Failure";
CM_ SG_ 2147483760 ERR_GDF "Gate driver Failure";
CM_ SG_ 2147483760 ERR_DUT "Discharge Cell Under Temperature Protection";
CM_ SG_ 2147483760 ERR_DOT "Discharge Cell Over Temperature Protection";
CM_ SG_ 2147483760 ERR_DOC "Discharge Over Current Protection";
CM_ SG_ 2147483760 ERR_CUT "Charge Cell Under Temperature Protection";
CM_ SG_ 2147483760 ERR_CUB "Cell Unbalance";
CM_ SG_ 2147483760 ERR_COT "Charge Cell Over Temperature Protection";
CM_ SG_ 2147483760 ERR_COC "Charge Over Current Protection";
CM_ SG_ 2147483760 ERR_AFF "AFE Failure";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "GenNWMApBusSleep" STRING ;
BA_DEF_  "GenNWMApCanNormal" STRING ;
BA_DEF_  "GenNWMApCanOff" STRING ;
BA_DEF_  "GenNWMApCanOn" STRING ;
BA_DEF_  "GenNWMApCanSleep" STRING ;
BA_DEF_  "GenNWMApCanWakeUp" STRING ;
BA_DEF_  "GenNWMGotoMode_Awake" STRING ;
BA_DEF_  "GenNWMGotoMode_BusSleep" STRING ;
BA_DEF_  "GenNWMSleepTime" INT 0 1000000;
BA_DEF_  "GenNWMTalkNM" STRING ;
BA_DEF_  "Manufacturer" STRING ;
BA_DEF_  "NmhBaseAddress" HEX 0 536870911;
BA_DEF_  "NmhMessageCount" INT 0 1024;
BA_DEF_  "NmType" STRING ;
BA_DEF_  "VAGTP20_SetupMessageCount" INT 0 255;
BA_DEF_  "VAGTP20_SetupStartAddress" HEX 0 2047;
BA_DEF_  "VersionDay" INT 1 31;
BA_DEF_  "VersionMonth" INT 1 12;
BA_DEF_  "VersionWeek" INT 1 52;
BA_DEF_  "VersionYear" INT 0 99;
BA_DEF_  "VersionNumber" INT 0 65535;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "Baudrate" INT 1 1000000;
BA_DEF_  "BaudrateCANFD" INT 1 16000000;
BA_DEF_  "NmhTimeoutTimer" INT 0 65535;
BA_DEF_  "NmhWaitBusSleepTimer" INT 0 65535;
BA_DEF_  "NmhPrepareBusSleepTimer" INT 0 65535;
BA_DEF_  "NmhLongTimer" INT 0 65535;
BA_DEF_  "NmhNStart" INT 0 255;
BA_DEF_ BU_  "GenNodAutoGenDsp" ENUM  "No","Yes";
BA_DEF_ BU_  "GenNodAutoGenSnd" ENUM  "No","Yes";
BA_DEF_ BU_  "GenNodSleepTime" INT 0 1000000;
BA_DEF_ BU_  "ILUsed" ENUM  "No","Yes";
BA_DEF_ BU_  "ECUVariantDefault" ENUM  "No","Yes";
BA_DEF_ BU_  "ECUVariantGroup" STRING ;
BA_DEF_ BU_  "VAGTP20_TargetAddress" HEX 0 127;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "NmhNode" ENUM  "No","Yes";
BA_DEF_ BU_  "NmNode" ENUM  "No","Yes";
BA_DEF_ BU_  "NmStationAddress" HEX 0 63;
BA_DEF_ BU_  "SamplePointMin" FLOAT 50 99.9;
BA_DEF_ BU_  "SamplePointMax" FLOAT 50 99.9;
BA_DEF_ BU_  "SyncJumpWidthMin" FLOAT 0 50;
BA_DEF_ BU_  "SyncJumpWidthMax" FLOAT 0 50;
BA_DEF_ BU_  "SyncJumpWidthCANFDMin" FLOAT 0 50;
BA_DEF_ BU_  "SyncJumpWidthCANFDMax" FLOAT 0 50;
BA_DEF_ BU_  "SamplePointCANFDMin" FLOAT 50 99.9;
BA_DEF_ BU_  "SamplePointCANFDMax" FLOAT 50 99.9;
BA_DEF_ BU_  "SSPOffsetCANFDMin" FLOAT 50 99.9;
BA_DEF_ BU_  "SSPOffsetCANFDMax" FLOAT 50 99.9;
BA_DEF_ BU_  "TimeQuantaMin" INT 4 257;
BA_DEF_ BU_  "TimeQuantaMax" INT 4 257;
BA_DEF_ BU_  "TimeQuantaCANFDMin" INT 4 257;
BA_DEF_ BU_  "TimeQuantaCANFDMax" INT 4 257;
BA_DEF_ BO_  "DiagRequest" ENUM  "No","Yes";
BA_DEF_ BO_  "DiagResponse" ENUM  "No","Yes";
BA_DEF_ BO_  "DiagState" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgChkConstant" HEX 0 4095;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 65535;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 100000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 100;
BA_DEF_ BO_  "GenMsgPDUConstants" STRING ;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","not_used","not_used","not_used","not_used","not_used","not_used","IfActive","NoMsgSendType";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 100000;
BA_DEF_ BO_  "NmMessage" ENUM  "No","Yes";
BA_DEF_ BO_  "NmhMessage" ENUM  "No","Yes";
BA_DEF_ BO_  "MsgType" ENUM  "Application","NM ","NMH","TP2.0","ISO-TP ","BAP","EID","KS","XCP_PRE_CONFIGURED","XCP_RUNTIME_CONFIGURED";
BA_DEF_ BO_  "VAGTP20_API" HEX 0 255;
BA_DEF_ BO_  "VAGTP20_DynConnection" ENUM  "No","Yes";
BA_DEF_ BO_  "VAGTP20_DynSetup" ENUM  "No","Yes";
BA_DEF_ BO_  "VAGTP20_StatConnection" ENUM  "No","Yes";
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ SG_  "GenSigActiveRepetitions" INT -2147483648 2147483647;
BA_DEF_ SG_  "GenSigInactiveValue" INT -2147483648 2147483647;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","OnChangeAndIfActive","OnChangeAndIfActiveWithRepetition";
BA_DEF_ SG_  "GenSigStartValue" INT -2147483648 2147483647;
BA_DEF_ SG_  "GenSigFuncType" ENUM  "NoFunction","MsgCounter","CRC-8","XOR","XOR-2","DatedBit","SW-Kl15","CHK","Constant","Protocol","CRC-8-SAE_J1850","CRC-32","DataLength","DataID","MsgCounter16","SOK-Signatur","Cluster";
BA_DEF_ SG_  "GenSigSwitchedByIgnition" ENUM  "No","Yes";
BA_DEF_ SG_  "Fehlerwert" HEX 0 2147483647;
BA_DEF_ SG_  "GenSigMissingSourceValue" HEX 0 2147483647;
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "GenNWMApBusSleep" "";
BA_DEF_DEF_  "GenNWMApCanNormal" "";
BA_DEF_DEF_  "GenNWMApCanOff" "";
BA_DEF_DEF_  "GenNWMApCanOn" "";
BA_DEF_DEF_  "GenNWMApCanSleep" "";
BA_DEF_DEF_  "GenNWMApCanWakeUp" "";
BA_DEF_DEF_  "GenNWMGotoMode_Awake" "";
BA_DEF_DEF_  "GenNWMGotoMode_BusSleep" "";
BA_DEF_DEF_  "GenNWMSleepTime" 0;
BA_DEF_DEF_  "GenNWMTalkNM" "";
BA_DEF_DEF_  "Manufacturer" "";
BA_DEF_DEF_  "NmhBaseAddress" 0;
BA_DEF_DEF_  "NmhMessageCount" 1;
BA_DEF_DEF_  "NmType" "None";
BA_DEF_DEF_  "VAGTP20_SetupMessageCount" 0;
BA_DEF_DEF_  "VAGTP20_SetupStartAddress" 0;
BA_DEF_DEF_  "VersionDay" 30;
BA_DEF_DEF_  "VersionMonth" 4;
BA_DEF_DEF_  "VersionWeek" 18;
BA_DEF_DEF_  "VersionYear" 20;
BA_DEF_DEF_  "VersionNumber" 0;
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "Baudrate" 500000;
BA_DEF_DEF_  "BaudrateCANFD" 2000000;
BA_DEF_DEF_  "NmhTimeoutTimer" 0;
BA_DEF_DEF_  "NmhWaitBusSleepTimer" 0;
BA_DEF_DEF_  "NmhPrepareBusSleepTimer" 0;
BA_DEF_DEF_  "NmhLongTimer" 0;
BA_DEF_DEF_  "NmhNStart" 0;
BA_DEF_DEF_  "GenNodAutoGenDsp" "Yes";
BA_DEF_DEF_  "GenNodAutoGenSnd" "Yes";
BA_DEF_DEF_  "GenNodSleepTime" 0;
BA_DEF_DEF_  "ILUsed" "Yes";
BA_DEF_DEF_  "ECUVariantDefault" "Yes";
BA_DEF_DEF_  "ECUVariantGroup" "";
BA_DEF_DEF_  "VAGTP20_TargetAddress" 0;
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "NmhNode" "Yes";
BA_DEF_DEF_  "NmNode" "No";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "SamplePointMin" 75;
BA_DEF_DEF_  "SamplePointMax" 75;
BA_DEF_DEF_  "SyncJumpWidthMin" 50;
BA_DEF_DEF_  "SyncJumpWidthMax" 50;
BA_DEF_DEF_  "SyncJumpWidthCANFDMin" 50;
BA_DEF_DEF_  "SyncJumpWidthCANFDMax" 50;
BA_DEF_DEF_  "SamplePointCANFDMin" 75;
BA_DEF_DEF_  "SamplePointCANFDMax" 75;
BA_DEF_DEF_  "SSPOffsetCANFDMin" 75;
BA_DEF_DEF_  "SSPOffsetCANFDMax" 75;
BA_DEF_DEF_  "TimeQuantaMin" 160;
BA_DEF_DEF_  "TimeQuantaMax" 160;
BA_DEF_DEF_  "TimeQuantaCANFDMin" 40;
BA_DEF_DEF_  "TimeQuantaCANFDMax" 40;
BA_DEF_DEF_  "DiagRequest" "No";
BA_DEF_DEF_  "DiagResponse" "No";
BA_DEF_DEF_  "DiagState" "No";
BA_DEF_DEF_  "GenMsgChkConstant" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgILSupport" "No";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgPDUConstants" "";
BA_DEF_DEF_  "GenMsgSendType" "NoMsgSendType";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "NmMessage" "No";
BA_DEF_DEF_  "NmhMessage" "No";
BA_DEF_DEF_  "MsgType" "Application";
BA_DEF_DEF_  "VAGTP20_API" 0;
BA_DEF_DEF_  "VAGTP20_DynConnection" "No";
BA_DEF_DEF_  "VAGTP20_DynSetup" "No";
BA_DEF_DEF_  "VAGTP20_StatConnection" "No";
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "GenSigActiveRepetitions" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigSendType" "NoSigSendType";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigFuncType" "NoFunction";
BA_DEF_DEF_  "GenSigSwitchedByIgnition" "Yes";
BA_DEF_DEF_  "Fehlerwert" 0;
BA_DEF_DEF_  "GenSigMissingSourceValue" 0;
BA_ "NmhNStart" 20;
BA_ "NmhLongTimer" 200;
BA_ "NmhPrepareBusSleepTimer" 500;
BA_ "NmhWaitBusSleepTimer" 750;
BA_ "NmhTimeoutTimer" 1000;
BA_ "BaudrateCANFD" 2000000;
BA_ "Baudrate" 500000;
BA_ "BusType" "CAN FD";
BA_ "VersionNumber" 1;
BA_ "VersionYear" 20;
BA_ "VersionWeek" 18;
BA_ "VersionMonth" 4;
BA_ "VersionDay" 30;
BA_ "VAGTP20_SetupStartAddress" 512;
BA_ "VAGTP20_SetupMessageCount" 128;
BA_ "NmType" "NM-High";
BA_ "NmhMessageCount" 1024;
BA_ "NmhBaseAddress" 452984832;
BA_ "Manufacturer" "VAG";
BA_ "GenNWMTalkNM" "Nmh_ReqNetOn()";
BA_ "GenNWMSleepTime" 4000;
BA_ "GenNWMGotoMode_BusSleep" "Nmh_ReqNetOff()";
BA_ "GenNWMGotoMode_Awake" "Nmh_ReqNetOn()";
BA_ "GenNWMApCanWakeUp" "apCanWakeUp()";
BA_ "GenNWMApCanSleep" "apCanSleep()";
BA_ "GenNWMApCanOn" "NMIndnetOn()";
BA_ "GenNWMApCanOff" "NMIndnetOff()";
BA_ "GenNWMApCanNormal" "apCanNormal()";
BA_ "GenNWMApBusSleep" "NMIndNetSDN()";
BA_ "DBName" "EVCANFD";
BA_ "TimeQuantaCANFDMax" BU_ BMS_MV 20;
BA_ "TimeQuantaCANFDMin" BU_ BMS_MV 20;
BA_ "TimeQuantaMax" BU_ BMS_MV 80;
BA_ "TimeQuantaMin" BU_ BMS_MV 80;
BA_ "SSPOffsetCANFDMax" BU_ BMS_MV 70;
BA_ "SSPOffsetCANFDMin" BU_ BMS_MV 70;
BA_ "SamplePointCANFDMax" BU_ BMS_MV 70;
BA_ "SamplePointCANFDMin" BU_ BMS_MV 70;
BA_ "SyncJumpWidthCANFDMax" BU_ BMS_MV 6;
BA_ "SyncJumpWidthCANFDMin" BU_ BMS_MV 6;
BA_ "SyncJumpWidthMax" BU_ BMS_MV 16;
BA_ "SyncJumpWidthMin" BU_ BMS_MV 16;
BA_ "SamplePointMax" BU_ BMS_MV 80;
BA_ "SamplePointMin" BU_ BMS_MV 80;
BA_ "NmNode" BU_ BMS_MV 0;
BA_ "NmhNode" BU_ BMS_MV 1;
BA_ "NodeLayerModules" BU_ BMS_MV "NMHigh.DLL";
BA_ "ECUVariantGroup" BU_ BMS_MV "BMS_MV";
BA_ "ECUVariantDefault" BU_ BMS_MV 1;
BA_ "ILUsed" BU_ BMS_MV 1;
BA_ "GenNodSleepTime" BU_ BMS_MV 4000;
BA_ "GenNodAutoGenSnd" BU_ BMS_MV 1;
BA_ "GenNodAutoGenDsp" BU_ BMS_MV 1;
BA_ "GenNodAutoGenSnd" BU_ RTE 1;
BA_ "GenNodAutoGenDsp" BU_ RTE 1;
BA_ "GenMsgDelayTime" BO_ 2645185858 6;
BA_ "GenMsgCycleTime" BO_ 2645185858 10;
BA_ "GenMsgSendType" BO_ 2645185858 0;
BA_ "VFrameFormat" BO_ 2645185858 15;
BA_ "GenMsgSendType" BO_ 2645185856 0;
BA_ "GenMsgDelayTime" BO_ 2645185856 90;
BA_ "GenMsgCycleTime" BO_ 2645185856 100;
BA_ "VFrameFormat" BO_ 2645185856 15;
BA_ "VFrameFormat" BO_ 2147483773 1;
BA_ "GenMsgCycleTime" BO_ 2147483773 100;
BA_ "GenMsgDelayTime" BO_ 2147483773 10;
BA_ "GenMsgSendType" BO_ 2147483773 0;
BA_ "GenMsgCycleTime" BO_ 2147487752 100;
BA_ "GenMsgDelayTime" BO_ 2147487752 8;
BA_ "GenMsgSendType" BO_ 2147487752 0;
BA_ "VFrameFormat" BO_ 2147487752 15;
BA_ "GenMsgDelayTime" BO_ 2147487746 4;
BA_ "GenMsgSendType" BO_ 2147487746 0;
BA_ "GenMsgCycleTime" BO_ 2147487746 10;
BA_ "VFrameFormat" BO_ 2147487746 15;
BA_ "GenMsgDelayTime" BO_ 2147487750 6;
BA_ "GenMsgCycleTime" BO_ 2147487750 100;
BA_ "GenMsgSendType" BO_ 2147487750 0;
BA_ "VFrameFormat" BO_ 2147487750 15;
BA_ "GenMsgCycleTimeFast" BO_ 2147487748 10;
BA_ "GenMsgCycleTime" BO_ 2147487748 10;
BA_ "GenMsgSendType" BO_ 2147487748 0;
BA_ "VFrameFormat" BO_ 2147487748 15;
BA_ "GenMsgDelayTime" BO_ 2147487744 2;
BA_ "GenMsgCycleTime" BO_ 2147487744 10;
BA_ "GenMsgSendType" BO_ 2147487744 0;
BA_ "VFrameFormat" BO_ 2147487744 15;
BA_ "GenMsgDelayTime" BO_ 2147484174 24;
BA_ "GenMsgCycleTime" BO_ 2147484174 100;
BA_ "GenMsgSendType" BO_ 2147484174 0;
BA_ "VFrameFormat" BO_ 2147484174 1;
BA_ "GenMsgILSupport" BO_ 2147484174 1;
BA_ "GenMsgDelayTime" BO_ 2147484172 22;
BA_ "GenMsgCycleTime" BO_ 2147484172 100;
BA_ "GenMsgSendType" BO_ 2147484172 0;
BA_ "VFrameFormat" BO_ 2147484172 1;
BA_ "GenMsgILSupport" BO_ 2147484172 1;
BA_ "GenMsgDelayTime" BO_ 2147484170 20;
BA_ "GenMsgCycleTime" BO_ 2147484170 100;
BA_ "GenMsgSendType" BO_ 2147484170 0;
BA_ "VFrameFormat" BO_ 2147484170 1;
BA_ "GenMsgILSupport" BO_ 2147484170 1;
BA_ "GenMsgDelayTime" BO_ 2147484169 16;
BA_ "GenMsgCycleTime" BO_ 2147484169 100;
BA_ "GenMsgSendType" BO_ 2147484169 0;
BA_ "VFrameFormat" BO_ 2147484169 1;
BA_ "GenMsgILSupport" BO_ 2147484169 1;
BA_ "GenMsgDelayTime" BO_ 2147484167 18;
BA_ "GenMsgSendType" BO_ 2147484167 0;
BA_ "GenMsgCycleTime" BO_ 2147484167 100;
BA_ "GenMsgILSupport" BO_ 2147484167 1;
BA_ "VFrameFormat" BO_ 2147484167 1;
BA_ "GenMsgDelayTime" BO_ 2147484165 14;
BA_ "GenMsgCycleTime" BO_ 2147484165 100;
BA_ "GenMsgSendType" BO_ 2147484165 0;
BA_ "VFrameFormat" BO_ 2147484165 1;
BA_ "GenMsgILSupport" BO_ 2147484165 1;
BA_ "GenMsgDelayTime" BO_ 2147484163 12;
BA_ "GenMsgCycleTime" BO_ 2147484163 100;
BA_ "GenMsgSendType" BO_ 2147484163 0;
BA_ "VFrameFormat" BO_ 2147484163 1;
BA_ "GenMsgILSupport" BO_ 2147484163 1;
BA_ "GenMsgDelayTime" BO_ 2147484161 10;
BA_ "GenMsgSendType" BO_ 2147484161 0;
BA_ "GenMsgCycleTime" BO_ 2147484161 100;
BA_ "VFrameFormat" BO_ 2147484161 1;
BA_ "GenMsgILSupport" BO_ 2147484161 1;
BA_ "GenMsgDelayTime" BO_ 2147483790 15;
BA_ "GenMsgCycleTime" BO_ 2147483790 100;
BA_ "GenMsgSendType" BO_ 2147483790 0;
BA_ "VFrameFormat" BO_ 2147483790 1;
BA_ "GenMsgDelayTime" BO_ 2147483788 10;
BA_ "GenMsgCycleTime" BO_ 2147483788 100;
BA_ "GenMsgSendType" BO_ 2147483788 0;
BA_ "VFrameFormat" BO_ 2147483788 1;
BA_ "GenMsgDelayTime" BO_ 2147483786 20;
BA_ "GenMsgCycleTime" BO_ 2147483786 100;
BA_ "GenMsgSendType" BO_ 2147483786 0;
BA_ "VFrameFormat" BO_ 2147483786 1;
BA_ "GenMsgDelayTime" BO_ 2147483784 5;
BA_ "GenMsgCycleTime" BO_ 2147483784 100;
BA_ "GenMsgSendType" BO_ 2147483784 0;
BA_ "VFrameFormat" BO_ 2147483784 1;
BA_ "GenMsgDelayTime" BO_ 2147483782 25;
BA_ "VFrameFormat" BO_ 2147483782 1;
BA_ "GenMsgCycleTime" BO_ 2147483782 100;
BA_ "GenMsgSendType" BO_ 2147483782 0;
BA_ "GenMsgDelayTime" BO_ 2147483763 38;
BA_ "GenMsgCycleTime" BO_ 2147483763 100;
BA_ "GenMsgSendType" BO_ 2147483763 0;
BA_ "VFrameFormat" BO_ 2147483763 1;
BA_ "GenMsgILSupport" BO_ 2147483763 1;
BA_ "GenMsgDelayTime" BO_ 2147483761 36;
BA_ "GenMsgCycleTime" BO_ 2147483761 100;
BA_ "GenMsgSendType" BO_ 2147483761 0;
BA_ "VFrameFormat" BO_ 2147483761 1;
BA_ "GenMsgILSupport" BO_ 2147483761 1;
BA_ "GenMsgDelayTime" BO_ 2147483759 34;
BA_ "GenMsgCycleTime" BO_ 2147483759 100;
BA_ "GenMsgSendType" BO_ 2147483759 0;
BA_ "VFrameFormat" BO_ 2147483759 1;
BA_ "GenMsgILSupport" BO_ 2147483759 1;
BA_ "GenMsgDelayTime" BO_ 2147483757 32;
BA_ "GenMsgCycleTime" BO_ 2147483757 100;
BA_ "GenMsgSendType" BO_ 2147483757 0;
BA_ "VFrameFormat" BO_ 2147483757 1;
BA_ "GenMsgILSupport" BO_ 2147483757 1;
BA_ "GenMsgDelayTime" BO_ 2147483755 30;
BA_ "VFrameFormat" BO_ 2147483755 1;
BA_ "GenMsgCycleTime" BO_ 2147483755 100;
BA_ "GenMsgSendType" BO_ 2147483755 0;
BA_ "GenMsgILSupport" BO_ 2147483755 1;
BA_ "GenMsgCycleTime" BO_ 2147483780 100;
BA_ "GenMsgSendType" BO_ 2147483780 0;
BA_ "VFrameFormat" BO_ 2147483780 15;
BA_ "GenMsgDelayTime" BO_ 2147483778 45;
BA_ "GenMsgCycleTime" BO_ 2147483778 100;
BA_ "GenMsgSendType" BO_ 2147483778 0;
BA_ "VFrameFormat" BO_ 2147483778 1;
BA_ "GenMsgDelayTime" BO_ 2147483776 35;
BA_ "GenMsgSendType" BO_ 2147483776 0;
BA_ "GenMsgCycleTime" BO_ 2147483776 100;
BA_ "VFrameFormat" BO_ 2147483776 1;
BA_ "VFrameFormat" BO_ 2147483772 1;
BA_ "GenMsgCycleTime" BO_ 2147483772 100;
BA_ "GenMsgSendType" BO_ 2147483772 0;
BA_ "GenMsgDelayTime" BO_ 2147483772 10;
BA_ "GenMsgDelayTime" BO_ 2147483774 25;
BA_ "VFrameFormat" BO_ 2147483774 1;
BA_ "GenMsgCycleTime" BO_ 2147483774 100;
BA_ "GenMsgSendType" BO_ 2147483774 0;
BA_ "VFrameFormat" BO_ 2147483749 1;
BA_ "GenMsgCycleTime" BO_ 2147483749 100;
BA_ "GenMsgSendType" BO_ 2147483749 0;
BA_ "GenMsgILSupport" BO_ 2147483749 1;
BA_ "GenMsgDelayTime" BO_ 2147483768 5;
BA_ "GenMsgCycleTime" BO_ 2147483768 100;
BA_ "GenMsgSendType" BO_ 2147483768 0;
BA_ "VFrameFormat" BO_ 2147483768 1;
BA_ "GenMsgDelayTime" BO_ 2147483753 7;
BA_ "VFrameFormat" BO_ 2147483753 1;
BA_ "GenMsgCycleTime" BO_ 2147483753 100;
BA_ "GenMsgSendType" BO_ 2147483753 0;
BA_ "GenMsgILSupport" BO_ 2147483753 1;
BA_ "GenMsgDelayTime" BO_ 2147483756 8;
BA_ "VFrameFormat" BO_ 2147483756 1;
BA_ "GenMsgCycleTime" BO_ 2147483756 100;
BA_ "GenMsgSendType" BO_ 2147483756 0;
BA_ "GenMsgDelayTime" BO_ 2147483751 5;
BA_ "VFrameFormat" BO_ 2147483751 1;
BA_ "GenMsgCycleTime" BO_ 2147483751 100;
BA_ "GenMsgSendType" BO_ 2147483751 0;
BA_ "GenMsgILSupport" BO_ 2147483751 1;
BA_ "GenMsgDelayTime" BO_ 2147483764 75;
BA_ "VFrameFormat" BO_ 2147483764 1;
BA_ "GenMsgCycleTime" BO_ 2147483764 100;
BA_ "GenMsgSendType" BO_ 2147483764 0;
BA_ "GenMsgDelayTime" BO_ 2147483750 2;
BA_ "VFrameFormat" BO_ 2147483750 1;
BA_ "GenMsgCycleTime" BO_ 2147483750 100;
BA_ "GenMsgSendType" BO_ 2147483750 0;
BA_ "GenMsgDelayTime" BO_ 2147483752 4;
BA_ "VFrameFormat" BO_ 2147483752 1;
BA_ "GenMsgCycleTime" BO_ 2147483752 100;
BA_ "GenMsgSendType" BO_ 2147483752 0;
BA_ "GenMsgDelayTime" BO_ 2147483754 6;
BA_ "VFrameFormat" BO_ 2147483754 1;
BA_ "GenMsgCycleTime" BO_ 2147483754 100;
BA_ "GenMsgSendType" BO_ 2147483754 0;
BA_ "GenMsgDelayTime" BO_ 2147483758 10;
BA_ "VFrameFormat" BO_ 2147483758 1;
BA_ "GenMsgCycleTime" BO_ 2147483758 100;
BA_ "GenMsgSendType" BO_ 2147483758 0;
BA_ "GenMsgCycleTime" BO_ 2147483770 100;
BA_ "GenMsgSendType" BO_ 2147483770 0;
BA_ "GenMsgDelayTime" BO_ 2147483770 12;
BA_ "VFrameFormat" BO_ 2147483770 15;
BA_ "VFrameFormat" BO_ 2147483748 1;
BA_ "GenMsgCycleTime" BO_ 2147483748 100;
BA_ "GenMsgSendType" BO_ 2147483748 0;
BA_ "GenMsgDelayTime" BO_ 2147483748 14;
BA_ "GenMsgDelayTime" BO_ 2147483762 65;
BA_ "VFrameFormat" BO_ 2147483762 1;
BA_ "GenMsgCycleTime" BO_ 2147483762 100;
BA_ "GenMsgSendType" BO_ 2147483762 0;
BA_ "GenMsgDelayTime" BO_ 2147483760 55;
BA_ "VFrameFormat" BO_ 2147483760 1;
BA_ "GenMsgCycleTime" BO_ 2147483760 100;
BA_ "GenMsgSendType" BO_ 2147483760 0;
BA_ "GenMsgDelayTime" BO_ 2147483766 85;
BA_ "GenMsgSendType" BO_ 2147483766 0;
BA_ "GenMsgCycleTime" BO_ 2147483766 100;
BA_ "VFrameFormat" BO_ 2147483766 15;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_31_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_30_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_29_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_28_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_27_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_26_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_25_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_24_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_23_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_22_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_21_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_20_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_19_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_18_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_17_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_16_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_15_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_14_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_13_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_12_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_11_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_10_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_9_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_8_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_7_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_6_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_5_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_4_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_3_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_2_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_1_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_EventID 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Second 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Day 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Month 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_Year 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_BasicSOC 255;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_TemperatureDMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_TemperatureDMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_TemperatureCMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_TemperatureCMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellTemperature4 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellTemperature3 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellTemperature2 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellTemperature1 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellTemperature0 32767;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2645185856 LOG_0_CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2147483773 hw_version 65535;
BA_ "GenSigStartValue" SG_ 2147487752 s_IMaxDischarge 38000;
BA_ "GenSigStartValue" SG_ 2147487752 s_IMaxCharge 38000;
BA_ "GenSigStartValue" SG_ 2147487752 s_UMinDischarge 840;
BA_ "GenSigStartValue" SG_ 2147487752 s_UMaxCharge 1651;
BA_ "GenSigStartValue" SG_ 2147487752 qQBatTotal_mu8 0;
BA_ "GenSigInactiveValue" SG_ 2147487752 przSOCOCV_mu16 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellMin 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellMax 255;
BA_ "GenSigStartValue" SG_ 2147487752 qprzBatSOC_mu8 0;
BA_ "GenSigStartValue" SG_ 2147487752 qprzSysSOCEP_mu8 0;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_9 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_8 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_7 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_6 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_5 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_4 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_3 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_2 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_11 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_10 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_1 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCCellArray_0 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 przBatSOC_mu16 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 SOCBmsBasicDyn 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 SOCBmsBasic 255;
BA_ "GenSigInactiveValue" SG_ 2147487752 s_przSOCBMS 255;
BA_ "GenSigStartValue" SG_ 2147487746 EOCBState 0;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_bShtDwnPrevHwResetCycle 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_bal_bCalcCompl 255;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_9 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_8 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_7 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_6 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_5 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_4 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_3 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_2 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_11 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_10 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_1 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_blBalStatusQualifierArray_0 0;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_11 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_10 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_9 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_8 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_7 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_6 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_5 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_4 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_3 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_2 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_1 255;
BA_ "GenSigInactiveValue" SG_ 2147487746 s_blBalStatusArray_0 255;
BA_ "GenSigStartValue" SG_ 2147487746 OMM_State 0;
BA_ "GenSigStartValue" SG_ 2147487746 AliveCounter 0;
BA_ "GenSigInactiveValue" SG_ 2147487746 UCellLpAvg 32767;
BA_ "GenSigInactiveValue" SG_ 2147487746 UCellMax 32767;
BA_ "GenSigInactiveValue" SG_ 2147487746 UCellDelta 32767;
BA_ "GenSigInactiveValue" SG_ 2147487746 UCellMin 32767;
BA_ "GenSigInactiveValue" SG_ 2147487746 SOCColdCrank 255;
BA_ "GenSigStartValue" SG_ 2147487746 enModeReq_mu8 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_valActualModeInt 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_valTargetModeBMS 0;
BA_ "GenSigStartValue" SG_ 2147487746 s_valActualModeBMS 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_PminColdCranking 0;
BA_ "GenSigInactiveValue" SG_ 2147487750 s_PminColdCranking 2147483647;
BA_ "GenSigStartValue" SG_ 2147487750 s_PCool 0;
BA_ "GenSigInactiveValue" SG_ 2147487750 s_PCool 2147483647;
BA_ "GenSigStartValue" SG_ 2147487750 s_PColdCrankingSupport 0;
BA_ "GenSigInactiveValue" SG_ 2147487750 s_PColdCrankingSupport 2147483647;
BA_ "GenSigStartValue" SG_ 2147487750 s_PBatColdCrank 0;
BA_ "GenSigInactiveValue" SG_ 2147487750 s_PBatColdCrank 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147487750 SpeedFan 65535;
BA_ "GenSigInactiveValue" SG_ 2147487750 PwmFan2 255;
BA_ "GenSigInactiveValue" SG_ 2147487750 PwmFan1 255;
BA_ "GenSigInactiveValue" SG_ 2147487750 TCellMin 32767;
BA_ "GenSigInactiveValue" SG_ 2147487750 TCellMax 32767;
BA_ "GenSigInactiveValue" SG_ 2147487750 TBat 32767;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrQAHCount 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTCellArray_4 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTCellArray_3 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTCellArray_2 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTCellArray_1 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTCellArray_0 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTOutlet 0;
BA_ "GenSigStartValue" SG_ 2147487750 s_valErrTInlet 0;
BA_ "GenSigStartValue" SG_ 2147487748 ser_qbEbatOK_mu8 0;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_TxQueueFullCounter_XL 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_TxQueueFullCounter_L 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_TxQueueFullCounter_M 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_TxQueueFullCounter_S 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_RxQueueFullCounter_XL 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_RxQueueFullCounter_L 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_RxQueueFullCounter_M 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 DBGCAN_RxQueueFullCounter_S 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_TxQueueFullCounter_XL 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_TxQueueFullCounter_S 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_TxQueueFullCounter_M 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_TxQueueFullCounter_L 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_RxQueueFullCounter_XL 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_RxQueueFullCounter_L 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_RxQueueFullCounter_M 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 VCAN_RxQueueFullCounter_S 255;
BA_ "GenSigInactiveValue" SG_ 2147487748 TargetModeMessageReceived 255;
BA_ "GenSigStartValue" SG_ 2147487748 s_valCrashShutdownSWReq 0;
BA_ "GenSigStartValue" SG_ 2147487748 s_valEResEmergency 0;
BA_ "GenSigStartValue" SG_ 2147487748 s_valDeepDischarge 0;
BA_ "GenSigStartValue" SG_ 2147487748 s_valErrStatusBMS 0;
BA_ "GenSigStartValue" SG_ 2147487748 s_valErrAirflow 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellMinMax 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_11 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_10 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_9 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_8 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_7 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_6 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_5 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_4 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_3 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_2 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_1 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUCellArray_0 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUKl40 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrUBat 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_valErrIBat 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_IBat 0;
BA_ "GenSigStartValue" SG_ 2147487744 s_IBatRaw 0;
BA_ "GenSigInactiveValue" SG_ 2147484174 SIM_Voltage_Stack 65535;
BA_ "GenSigInactiveValue" SG_ 2147484174 SIM_Voltage_Link 65535;
BA_ "GenSigInactiveValue" SG_ 2147484174 SIM_Voltage_12VADC2 65535;
BA_ "GenSigStartValue" SG_ 2147484174 SIM_Voltage_12VADC2 0;
BA_ "GenSigInactiveValue" SG_ 2147484174 SIM_Voltage_12VADC1 65535;
BA_ "GenSigInactiveValue" SG_ 2147484172 SIM_Temperature_FanOut 32767;
BA_ "GenSigInactiveValue" SG_ 2147484172 SIM_Temperature_FanIn 32767;
BA_ "GenSigInactiveValue" SG_ 2147484170 SIM_Temperature_DMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2147484170 SIM_Temperature_DMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2147484170 SIM_Temperature_CMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2147484170 SIM_Temperature_CMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2147484169 SIM_Current_Shunt 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147484169 SIM_Current_HallSensor 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147484167 SIM_Temperature_Cell4 127;
BA_ "GenSigInactiveValue" SG_ 2147484167 SIM_Temperature_Cell3 127;
BA_ "GenSigInactiveValue" SG_ 2147484167 SIM_Temperature_Cell2 127;
BA_ "GenSigInactiveValue" SG_ 2147484167 SIM_Temperature_Cell1 127;
BA_ "GenSigInactiveValue" SG_ 2147484167 SIM_Temperature_Cell0 127;
BA_ "GenSigInactiveValue" SG_ 2147484165 SIM_Voltage_Cell11 65535;
BA_ "GenSigInactiveValue" SG_ 2147484165 SIM_Voltage_Cell10 65535;
BA_ "GenSigInactiveValue" SG_ 2147484165 SIM_Voltage_Cell9 65535;
BA_ "GenSigInactiveValue" SG_ 2147484165 SIM_Voltage_Cell8 65535;
BA_ "GenSigInactiveValue" SG_ 2147484163 SIM_Voltage_Cell7 65535;
BA_ "GenSigInactiveValue" SG_ 2147484163 SIM_Voltage_Cell6 65535;
BA_ "GenSigInactiveValue" SG_ 2147484163 SIM_Voltage_Cell5 65535;
BA_ "GenSigInactiveValue" SG_ 2147484163 SIM_Voltage_Cell4 65535;
BA_ "GenSigInactiveValue" SG_ 2147484161 SIM_Voltage_Cell3 65535;
BA_ "GenSigInactiveValue" SG_ 2147484161 SIM_Voltage_Cell2 65535;
BA_ "GenSigInactiveValue" SG_ 2147484161 SIM_Voltage_Cell1 65535;
BA_ "GenSigInactiveValue" SG_ 2147484161 SIM_Voltage_Cell0 65535;
BA_ "GenSigInactiveValue" SG_ 2147483790 OffsetTempVolt_Tcell05 32767;
BA_ "GenSigStartValue" SG_ 2147483790 OffsetTempVolt_Tcell05 32766;
BA_ "GenSigInactiveValue" SG_ 2147483790 OffsetTempVolt_T_FAN_Outlet 32767;
BA_ "GenSigStartValue" SG_ 2147483790 OffsetTempVolt_T_FAN_Outlet 32766;
BA_ "GenSigInactiveValue" SG_ 2147483790 OffsetTempVolt_T_FAN_Inlet 32767;
BA_ "GenSigStartValue" SG_ 2147483790 OffsetTempVolt_T_FAN_Inlet 32766;
BA_ "GenSigInactiveValue" SG_ 2147483788 OffsetTempVolt_Tcell04 32767;
BA_ "GenSigStartValue" SG_ 2147483788 OffsetTempVolt_Tcell04 32766;
BA_ "GenSigInactiveValue" SG_ 2147483788 OffsetTempVolt_Tcell03 32767;
BA_ "GenSigStartValue" SG_ 2147483788 OffsetTempVolt_Tcell03 32766;
BA_ "GenSigInactiveValue" SG_ 2147483788 OffsetTempVolt_Tcell02 32767;
BA_ "GenSigStartValue" SG_ 2147483788 OffsetTempVolt_Tcell02 32766;
BA_ "GenSigInactiveValue" SG_ 2147483788 OffsetTempVolt_Tcell01 32767;
BA_ "GenSigStartValue" SG_ 2147483788 OffsetTempVolt_Tcell01 32766;
BA_ "GenSigInactiveValue" SG_ 2147483786 CaliPackVolADC_Low 65535;
BA_ "GenSigStartValue" SG_ 2147483786 CaliPackVolADC_Low 65534;
BA_ "GenSigInactiveValue" SG_ 2147483786 CaliPackVolADC_High 65535;
BA_ "GenSigStartValue" SG_ 2147483786 CaliPackVolADC_High 65534;
BA_ "GenSigInactiveValue" SG_ 2147483784 CaliCurrentADC_Low 2147483647;
BA_ "GenSigStartValue" SG_ 2147483784 CaliCurrentADC_Low 2147483646;
BA_ "GenSigInactiveValue" SG_ 2147483784 CaliCurrentADC_High 2147483647;
BA_ "GenSigStartValue" SG_ 2147483784 CaliCurrentADC_High 2147483646;
BA_ "GenSigInactiveValue" SG_ 2147483782 VoltageCal_state 15;
BA_ "GenSigStartValue" SG_ 2147483782 VoltageCal_state 14;
BA_ "GenSigInactiveValue" SG_ 2147483782 TemperatureCal_state 15;
BA_ "GenSigStartValue" SG_ 2147483782 TemperatureCal_state 14;
BA_ "GenSigInactiveValue" SG_ 2147483782 CurrentCal_state 15;
BA_ "GenSigStartValue" SG_ 2147483782 CurrentCal_state 14;
BA_ "GenSigStartValue" SG_ 2147483782 Calibration_status 6;
BA_ "GenSigInactiveValue" SG_ 2147483782 Calibration_status 7;
BA_ "GenSigInactiveValue" SG_ 2147483763 RefTempVolt_Tcell05 32767;
BA_ "GenSigInactiveValue" SG_ 2147483763 RefTempVolt_T_FAN_Outlet 32767;
BA_ "GenSigInactiveValue" SG_ 2147483763 RefTempVolt_T_FAN_Inlet 32767;
BA_ "GenSigInactiveValue" SG_ 2147483761 RefTempVolt_Tcell04 32767;
BA_ "GenSigInactiveValue" SG_ 2147483761 RefTempVolt_Tcell03 32767;
BA_ "GenSigInactiveValue" SG_ 2147483761 RefTempVolt_Tcell02 32767;
BA_ "GenSigInactiveValue" SG_ 2147483761 RefTempVolt_Tcell01 32767;
BA_ "GenSigInactiveValue" SG_ 2147483759 RefPackVolt_Low 65535;
BA_ "GenSigInactiveValue" SG_ 2147483759 RefPackVolt_High 65535;
BA_ "GenSigInactiveValue" SG_ 2147483757 RefCurrent_Low 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147483757 RefCurrent_High 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147483755 VoltageCal_state_request 7;
BA_ "GenSigInactiveValue" SG_ 2147483755 TemperatureCal_state_request 7;
BA_ "GenSigInactiveValue" SG_ 2147483755 CurrentCal_state_request 7;
BA_ "GenSigInactiveValue" SG_ 2147483755 Calibration_request 7;
BA_ "GenSigInactiveValue" SG_ 2147483755 VoltageCal_MeasTime 31;
BA_ "GenSigInactiveValue" SG_ 2147483755 TemperatureCal_MeasTime 31;
BA_ "GenSigInactiveValue" SG_ 2147483755 CurrentCal_MeasTime 31;
BA_ "GenSigInactiveValue" SG_ 2147483780 CellTemperature_4 32767;
BA_ "GenSigInactiveValue" SG_ 2147483780 CellTemperature_3 32767;
BA_ "GenSigInactiveValue" SG_ 2147483780 CellTemperature_2 32767;
BA_ "GenSigInactiveValue" SG_ 2147483780 CellTemperature_1 32767;
BA_ "GenSigInactiveValue" SG_ 2147483780 CellTemperature_0 32767;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV8 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV7 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV6 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV5 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV4 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV3 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV2 15;
BA_ "GenSigInactiveValue" SG_ 2147483778 ERR_RSV1 15;
BA_ "GenSigInactiveValue" SG_ 2147483776 EMBDebugData_32_1 0;
BA_ "GenSigInactiveValue" SG_ 2147483772 svn_revison 65535;
BA_ "GenSigInactiveValue" SG_ 2147483772 patch 65535;
BA_ "GenSigInactiveValue" SG_ 2147483772 minor 65535;
BA_ "GenSigInactiveValue" SG_ 2147483772 major 65535;
BA_ "GenSigInactiveValue" SG_ 2147483774 EMBDebugData_16_4 65535;
BA_ "GenSigInactiveValue" SG_ 2147483774 EMBDebugData_16_3 65535;
BA_ "GenSigInactiveValue" SG_ 2147483774 EMBDebugData_16_2 65535;
BA_ "GenSigInactiveValue" SG_ 2147483774 EMBDebugData_16_1 65535;
BA_ "GenSigInactiveValue" SG_ 2147483749 Fan02Duty_set 255;
BA_ "GenSigInactiveValue" SG_ 2147483749 Fan01Duty_set 255;
BA_ "GenSigInactiveValue" SG_ 2147483749 SCP_factor_data 255;
BA_ "GenSigInactiveValue" SG_ 2147483768 SCP_factor 255;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_RTCAlarmInMinutes 16383;
BA_ "GenSigStartValue" SG_ 2147483753 Set_RTCAlarmInMinutes 1;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Second 255;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Day 255;
BA_ "GenSigStartValue" SG_ 2147483753 Set_Day 1;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Month 255;
BA_ "GenSigStartValue" SG_ 2147483753 Set_Month 1;
BA_ "GenSigInactiveValue" SG_ 2147483753 Set_Year 255;
BA_ "GenSigInactiveValue" SG_ 2147483756 CrashDutyT1 255;
BA_ "GenSigInactiveValue" SG_ 2147483756 CrashPeriod 255;
BA_ "GenSigInactiveValue" SG_ 2147483756 CrashLevel 255;
BA_ "GenSigInactiveValue" SG_ 2147483750 CellVoltage0 65535;
BA_ "GenSigInactiveValue" SG_ 2147483750 CellVoltage1 65535;
BA_ "GenSigInactiveValue" SG_ 2147483750 CellVoltage2 65535;
BA_ "GenSigInactiveValue" SG_ 2147483750 CellVoltage3 65535;
BA_ "GenSigInactiveValue" SG_ 2147483752 CellVoltage4 65535;
BA_ "GenSigInactiveValue" SG_ 2147483752 CellVoltage5 65535;
BA_ "GenSigInactiveValue" SG_ 2147483752 CellVoltage6 65535;
BA_ "GenSigInactiveValue" SG_ 2147483752 CellVoltage7 65535;
BA_ "GenSigInactiveValue" SG_ 2147483754 CellVoltage8 65535;
BA_ "GenSigInactiveValue" SG_ 2147483754 CellVoltage9 65535;
BA_ "GenSigInactiveValue" SG_ 2147483754 CellVoltage10 65535;
BA_ "GenSigInactiveValue" SG_ 2147483754 CellVoltage11 65535;
BA_ "GenSigInactiveValue" SG_ 2147483758 HallSensorCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147483758 ShuntCurrent 2147483647;
BA_ "GenSigInactiveValue" SG_ 2147483770 Temp_DMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2147483770 Temp_DMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2147483770 Temp_CMOS01 32767;
BA_ "GenSigInactiveValue" SG_ 2147483770 Temp_CMOS02 32767;
BA_ "GenSigInactiveValue" SG_ 2147483770 TempFanIn 32767;
BA_ "GenSigInactiveValue" SG_ 2147483770 TempFanOut 32767;
BA_ "GenSigInactiveValue" SG_ 2147483748 StackVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2147483748 LinkVoltage 65535;
BA_ "GenSigInactiveValue" SG_ 2147483748 Diag_12VADC1 65535;
BA_ "GenSigStartValue" SG_ 2147483748 Diag_12VADC2 0;
BA_ "GenSigInactiveValue" SG_ 2147483748 Diag_12VADC2 65535;
BA_ "GenSigInactiveValue" SG_ 2147483762 Fan02Rpm 65535;
BA_ "GenSigInactiveValue" SG_ 2147483762 Fan02Duty_out 255;
BA_ "GenSigInactiveValue" SG_ 2147483762 Fan_active 255;
BA_ "GenSigInactiveValue" SG_ 2147483762 Fan01Duty_out 255;
BA_ "GenSigInactiveValue" SG_ 2147483762 Fan01Rpm 65535;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_FUSE 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_UVP 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_SCP 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_OVP 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_MOT 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_MFF 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_GDF 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_DUT 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_DOT 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_DOC 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_CUT 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_CUB 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_COT 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_COC 15;
BA_ "GenSigInactiveValue" SG_ 2147483760 ERR_AFF 15;
BA_ "GenSigInactiveValue" SG_ 2147483766 RTCAlarm_Second 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 RTCAlarm_Minute 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 RTCAlarm_Hour 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 RTCAlarm_Day 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Year 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Month 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Day 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Hour 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Minute 255;
BA_ "GenSigInactiveValue" SG_ 2147483766 Second 255;
VAL_ 2645185856 LOG_31_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_30_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_29_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_28_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_27_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_26_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_25_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_24_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_23_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_22_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_21_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_20_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_19_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_18_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_17_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_16_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_15_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_14_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_13_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_12_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_11_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_10_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_9_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_8_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_7_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_6_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_5_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_4_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_3_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_2_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_1_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2645185856 LOG_0_EventID 52 "GDF2_ERR_LV3" 51 "GDF1_ERR_LV3" 50 "FUSE_LV4" 49 "PRECHARGE_TO" 48 "FAN_ROTOR_LOCKED" 47 "AIRDUCT_BLOCKED" 46 "SOC_DD_LV3" 45 "SOC_DD_LV2" 44 "SOC_DD_LV1" 43 "MVK_01_TO" 42 "CRASH_CAN_IMPL" 41 "CRASH_CAN_ERRVAL" 40 "CRASH_LV2" 39 "CRASH_LV1" 38 "GDF2_SPI_LV4" 37 "GDF1_SPI_LV4" 36 "MFF_LV4" 35 "AFF_LV4" 34 "MOT_LV3" 33 "MOT_LV2" 32 "MOT_LV1" 31 "SCP_LV4" 30 "DOC_LV3" 29 "DOC_LV1" 28 "COC_LV3" 27 "COC_LV2" 26 "COC_LV1" 25 "DUT_LV4" 24 "DUT_LV3" 23 "DUT_LV2" 22 "DUT_LV1" 21 "DOT_LV4" 20 "DOT_LV3" 19 "DOT_LV2" 18 "DOT_LV1" 17 "CUT_LV4" 16 "CUT_LV3" 15 "CUT_LV2" 14 "CUT_LV1" 13 "COT_LV4" 12 "COT_LV3" 11 "COT_LV2" 10 "COT_LV1" 9 "CUB_LV4" 8 "UVP_LV4" 7 "UVP_LV3" 6 "UVP_LV2" 5 "UVP_LV1" 4 "OVP_LV4" 3 "OVP_LV3" 2 "OVP_LV2" 1 "OVP_LV1" 0 "NONE" ;
VAL_ 2147487746 NMH_State 2 "NMH_state_prepare_bus_sleep" 1 "NMH_state_ready_sleep" 0 "NMH_state_bus_sleep" ;
VAL_ 2147487746 s_blBalStatusArray_11 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_10 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_9 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_8 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_7 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_6 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_5 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_4 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_3 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_2 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_1 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 s_blBalStatusArray_0 2 "Init" 1 "Balancing On" 0 "Balancing Off" ;
VAL_ 2147487746 OMM_State 15 "OMM_STATE_SNA" 13 "RELAY_wait_open" 12 "RELAY_wait_close" 11 "POWER_ON" 10 "DIAG_EMERGENCY_OFF" 9 "EMERGENCY_OFF" 8 "BAT_POWER_ON" 7 "PRECHARGE" 6 "BAT_POWER_OFF" 5 "DIAG_INIT" 4 "INIT" 3 "POWER_DOWN" 2 "LOCAL_ACTIVE" 1 "PREPARE_DEEP_SLEEP" 0 "PREPARE_SLEEP" ;
VAL_ 2147487746 enModeReq_mu8 5 "INIT" 4 "MV_ON" 3 "MV_OFF" 2 "EMERGENCY_OFF" ;
VAL_ 2147487746 s_valActualModeInt 11 "DIAG_EMERGENCY_OFF" 10 "DIAG_INIT" 2 "EMERGENCY_OFF" 3 "MV_OFF" 4 "MV_ON" 5 "INIT" 6 "LOCAL_ACTIVE" 7 "PRECHARGE" 9 "RUN_ON" ;
VAL_ 2147487746 s_valTargetModeBMS 7 "INIT" 1 "MV_ON" 0 "MV_OFF" ;
VAL_ 2147487746 s_valActualModeBMS 7 "INIT" 5 "EMERGENCY_OFF" 1 "MV_ON" 0 "MV_OFF" ;
VAL_ 2147487748 F_FUSE_FAILURE 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_PRECHARGE_TO 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_FAN_ROTOR_LOCKED 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_AIRDUCT_BLOCKED 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOC_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOC_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOC_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COC_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COC_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COC_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_SOC_DD_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_SOC_DD_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_SOC_DD_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_MVK_01_TO 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CRASH_CAN_IMPL 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CRASH_CAN_ERRVAL 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CRASH_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CRASH_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_GDR_FAILURE 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_AFE_FAILURE 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_MOSFET_FAILURE 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_MOSFET_OT_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_MOSFET_OT_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_MOSFET_OT_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_SCP 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DUT_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DUT_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DUT_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DUT_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOT_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOT_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOT_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_DOT_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CUT_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CUT_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CUT_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CUT_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COT_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COT_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COT_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_COT_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_CELL_UNBALANCE_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_UVP_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_UVP_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_UVP_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_UVP_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_OVP_L4 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_OVP_L3 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_OVP_L2 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147487748 F_OVP_L1 2 "Init - Not Tested" 1 "Fail" 0 "Pass" ;
VAL_ 2147483782 VoltageCal_state 15 "SNA" 14 "INIT" 13 "VCAL_state_error" 12 "VCAL_state_duration_error" 11 "VCAL_state_Invalid_req" 10 "VCAL_state_seq_error" 9 "VCAL_state_finished" 8 "VCAL_state_storing" 7 "VCAL_state_calculating" 6 "VCAL_state_high_cal_done" 5 "VCAL_state_high_cal_active" 4 "VCAL_state_high_cal_init" 3 "VCAL_state_low_cal_done" 2 "VCAL_state_low_cal_active" 1 "VCAL_state_low_cal_init" 0 "VCAL_state_off" ;
VAL_ 2147483782 TemperatureCal_state 15 "SNA" 14 "INIT" 13 "Invalid" 12 "Invalid" 11 "Invalid" 10 "TCAL_state_error" 9 "TCAL_state_duration_error" 8 "TCAL_state_Invalid_req" 7 "TCAL_state_seq_error" 6 "TCAL_state_finished" 5 "TCAL_state_storing" 4 "TCAL_state_calculating" 3 "TCAL_state_cal_done" 2 "TCAL_state_cal_active" 1 "TCAL_state_cal_init" 0 "TCAL_state_off" ;
VAL_ 2147483782 CurrentCal_state 15 "SNA" 14 "INIT" 13 "CCAL_state_error" 12 "CCAL_state_duration_error" 11 "CCAL_state_Invalid_req" 10 "CCAL_state_seq_error" 9 "CCAL_state_finished" 8 "CCAL_state_storing" 7 "CCAL_state_calculating" 6 "CCAL_state_high_cal_done" 5 "CCAL_state_high_cal_active" 4 "CCAL_state_high_cal_init" 3 "CCAL_state_low_cal_done" 2 "CCAL_state_low_cal_active" 1 "CCAL_state_low_cal_init" 0 "CCAL_state_off" ;
VAL_ 2147483782 Calibration_status 7 "SNA" 6 "Calibration_not_available" 5 "Invalid_Calibration_request" 4 "Calibration_sequence_error" 3 "Temperature_Mode_active" 2 "Voltage_Mode_active" 1 "Current_Mode_active" 0 "Calibration_inactive" ;
VAL_ 2147483755 VoltageCal_state_request 7 "SNA" 6 "Invalid" 5 "VCAL_state_req_exit" 4 "VCAL_state_req_high_cal" 3 "VCAL_state_req_init_high_cal" 2 "VCAL_state_req_low_cal" 1 "VCAL_state_req_init_low_cal" 0 "VCAL_state_req_off" ;
VAL_ 2147483755 TemperatureCal_state_request 7 "SNA" 6 "Invalid" 5 "Invalid" 4 "Invalid" 3 "TCAL_state_req_exit" 2 "TCAL_state_req_cal" 1 "TCAL_state_req_init_cal" 0 "TCAL_state_req_off" ;
VAL_ 2147483755 CurrentCal_state_request 7 "SNA" 6 "Invalid" 5 "CCAL_state_req_exit" 4 "CCAL_state_req_high_cal" 3 "CCAL_state_req_init_high_cal" 2 "CCAL_state_req_low_cal" 1 "CCAL_state_req_init_low_cal" 0 "CCAL_state_req_off" ;
VAL_ 2147483755 Calibration_request 7 "SNA" 6 "Invalid" 5 "Invalid" 4 "Invalid" 3 "Temperature_Mode_requested" 2 "Voltage_Mode_requested" 1 "Current_Mode_requested" 0 "No_Calibration_requested" ;

