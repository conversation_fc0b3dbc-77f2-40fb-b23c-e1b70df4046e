VERSION ""


NS_ :
        NS_DESC_
        CM_
        BA_DEF_
        BA_
        VAL_
        CAT_DEF_
        CAT_
        FILTER
        BA_DEF_DEF_
        EV_DATA_
        ENVVAR_DATA_
        SGTYPE_
        SGTYPE_VAL_
        BA_DEF_SGTYPE_
        BA_SGTYPE_
        SIG_TYPE_REF_
        VAL_TABLE_
        SIG_GROUP_
        SIG_VALTYPE_
        SIGTYPE_VALTYPE_
        BO_TX_BU_
        BA_DEF_REL_
        BA_REL_
        BA_DEF_DEF_REL_
        BU_SG_REL_
        BU_EV_REL_
        BU_BO_REL_
        SG_MUL_VAL_

BS_:

BU_: Project test

BO_ 1875 BMS_BasicInfo: 8 test
 SG_ BMS_command M : 0|16@1+ (1,0) [0|65535] ""  Project
 SG_ BMS_Vpack : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_PackVoltage : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_PackCurrent : 48|16@1- (1,0) [-32768|32767] "mA"  Project

BO_ 1876 BMS_Status: 8 test
 SG_ BMS_AvgCurrent : 0|16@1- (1,0) [-32768|32767] "mA"  Project
 SG_ BMS_RSOC : 16|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_ASOC : 24|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_EngMode : 32|8@1+ (1,0) [0|255] "Hex"  Project
 SG_ BMS_SystemTime : 40|24@1+ (1,0) [0|16777215] "DateTime"  Project

BO_ 1877 BMS_Capacity: 8 test
 SG_ BMS_RC : 0|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_FCC : 16|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_CycleCount : 32|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_LearnCycle : 48|16@1+ (1,0) [0|65535] "Times"  Project

BO_ 1878 BMS_AdvCapacity: 8 test
 SG_ BMS_UserRC : 0|16@1+ (1,0) [0|65535] "mAh"  Project
 SG_ BMS_UserRSOC : 16|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_FCCmin : 24|16@1+ (1,0) [0|65535] "100mWh"  Project
 SG_ BMS_DeltaRC : 40|16@1+ (1,0) [0|65535] "mAh"  Project

BO_ 1879 BMS_StatusFlags: 8 test
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex"  Project

BO_ 1880 BMS_SafetyStatus: 8 test
 SG_ BMS_SafetyStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_SafetyStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_WarnStatus : 32|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_STStatus : 48|16@1+ (1,0) [0|65535] "Hex"  Project

BO_ 1881 BMS_ProtectionStatus: 8 test
 SG_ BMS_PFStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_PFStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_CBS0_15 : 32|16@1+ (1,0) [0|65535] "Hex"  Project

BO_ 1882 BMS_Temp1_4: 8 test
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project

BO_ 1883 BMS_Temp5_8: 8 test
 SG_ BMS_Temp5 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp6 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp7 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp8 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project

BO_ 1884 BMS_Temp9_12: 8 test
 SG_ BMS_Temp9 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp10 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp11 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project
 SG_ BMS_Temp12 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  Project

BO_ 1885 BMS_CellVolt1_4: 8 test
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1886 BMS_CellVolt5_8: 8 test
 SG_ BMS_CellVolt5 : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt6 : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt7 : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt8 : 48|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1887 BMS_CellVolt9_12: 8 test
 SG_ BMS_CellVolt9 : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt10 : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt11 : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt12 : 48|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1888 BMS_CellVolt13_16: 8 test
 SG_ BMS_CellVolt13 : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt14 : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt15 : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_CellVolt16 : 48|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1889 BMS_bqGauge: 8 test
 SG_ BMS_bqVoltage : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_bqCurrent : 16|16@1- (1,0) [-32768|32767] "mA"  Project
 SG_ BMS_bqSOC : 32|8@1+ (1,0) [0|255] "%"  Project
 SG_ BMS_bqTemp : 40|16@1- (0.1,0) [-3276.8|3276.7] "C"  Project

BO_ 1890 BMS_bqCapacity: 8 test
 SG_ BMS_bqRC : 0|16@1+ (4,0) [0|262140] "mAh"  Project
 SG_ BMS_bqFCC : 16|16@1+ (4,0) [0|262140] "mAh"  Project
 SG_ BMS_bqCycleCnt : 32|16@1+ (1,0) [0|65535] "Times"  Project
 SG_ BMS_bqSOH : 48|16@1+ (1,0) [0|65535] "%"  Project

BO_ 1891 BMS_bqStatus: 8 test
 SG_ BMS_bqCtlStatus : 0|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqFlags : 16|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqFlagsB : 32|16@1+ (1,0) [0|65535] "Hex"  Project
 SG_ BMS_bqPackConfig : 48|16@1+ (1,0) [0|65535] "Hex"  Project

BO_ 1892 BMS_bqEnergy: 8 test
 SG_ BMS_bqAvailableEngy : 0|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_bqFCC_cWH : 16|16@1+ (560,0) [0|36699600] "mWh"  Project
 SG_ BMS_bqDesignCapacity : 32|16@1+ (4,0) [0|262140] "mAh"  Project

BO_ 1893 BMS_bqCharging: 8 test
 SG_ BMS_bqChgVolt : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_bqPassedCharge : 16|16@1+ (1,0) [0|65535] "mAh"  Project

BO_ 1894 BMS_Voltages: 8 test
 SG_ BMS_Vdfuse : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_VchgPlus : 16|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_VpackPlus : 32|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_Vcfuse : 48|16@1+ (1,0) [0|65535] "mV"  Project

BO_ 1895 BMS_Insulation: 8 test
 SG_ BMS_V_insulation : 0|16@1+ (1,0) [0|65535] "mV"  Project
 SG_ BMS_InsulResP : 16|16@1+ (1,0) [0|65535] "KOhm"  Project
 SG_ BMS_InsulResN : 32|16@1+ (1,0) [0|65535] "KOhm"  Project

BO_ 1896 BMS_Version: 8 test
 SG_ BMS_PublicVer : 0|24@1+ (1,0) [0|16777215] "Ver"  Project
 SG_ BMS_ChkSum : 24|32@1+ (1,0) [0|4294967295] "Hex"  Project

